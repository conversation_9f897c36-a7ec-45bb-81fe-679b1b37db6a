# 连线优化改进说明

## 概述

针对您提出的"线尽量不要从节点中间穿过，并且多条线尽量不要重叠在一起"的需求，我们对边路径优化器进行了全面改进。

## 主要改进内容

### 1. 避免节点穿越

#### 改进前的问题
- 连接线可能直接穿过中间节点
- 安全边距不够，线条与节点过于接近

#### 改进后的解决方案
- **增强安全边距检测**：使用 `LayoutConfig.PATH_SAFETY_MARGIN` (80px) 作为安全距离
- **智能路径规划**：多种延伸距离尝试 (80, 120, 160, 200, 250, 300px)
- **动态避障算法**：实时检测并避开所有障碍节点

```java
// 使用配置的安全边距
double safetyMargin = LayoutConfig.PATH_SAFETY_MARGIN; // 80px

// 多种延伸距离尝试
double[] extensions = {80, 120, 160, 200, 250, 300};
```

### 2. 避免线条重叠

#### 改进前的问题
- 多条连接线可能重叠在同一路径上
- 没有线条分离机制

#### 改进后的解决方案
- **路径重叠检测**：检查新路径是否与已有路径过于接近
- **线条分离距离**：使用 `LayoutConfig.LINE_SEPARATION_DISTANCE` (25px) 保持线条间距
- **智能路径选择**：为每条线选择不同的路径避免重叠

```java
// 检查路径重叠
private static boolean isPathOverlappingWithExisting(double x1, double y1, double x2, double y2, 
                                                   List<List<RoutePoint>> existingPaths) {
    double overlapTolerance = LayoutConfig.LINE_SEPARATION_DISTANCE; // 25px
    // 检测逻辑...
}
```

### 3. 改进的算法流程

#### 新的优化流程
1. **节点矩形构建**：为所有节点创建扩展的安全区域
2. **逐条路径计算**：按顺序为每条边计算最优路径
3. **重叠避免**：每条新路径都检查与已有路径的重叠
4. **多策略尝试**：水平优先、垂直优先、备用策略
5. **路径记录**：将计算好的路径加入已有路径列表

#### 核心改进方法
```java
// 主要优化方法
public static void optimizeAllEdgePaths(Map<String, NodeInfo> nodes, List<EdgeInfo> edges)

// 避免重叠的路径计算
private static boolean optimizeEdgePathWithOverlapAvoidance(EdgeInfo edge, Map<String, NodeInfo> nodes, 
                                                          List<NodeRect> nodeRects, List<List<RoutePoint>> existingPaths)

// 重叠检测
private static boolean areLineSegmentsTooClose(double x1, double y1, double x2, double y2,
                                             double x3, double y3, double x4, double y4, double tolerance)
```

## 配置参数

### 关键配置参数
```java
// 路径安全参数
public static final double PATH_SAFETY_MARGIN = 80.0;       // 路径与节点的安全距离
public static final double LINE_SEPARATION_DISTANCE = 25.0; // 多条线之间的分离距离
public static final double NODE_EDGE_MARGIN = 18.0;         // 节点边缘到连接点的距离

// 路径计算参数
public static final boolean USE_ORTHOGONAL_ROUTING = true;   // 使用正交路由
public static final double ROUTE_GRID_SIZE = 20.0;          // 路由网格大小
public static final double MIN_PATH_SEGMENT_LENGTH = 40.0;   // 最小路径段长度
```

## 使用方法

### 1. 在 ProcessModelLayouter 中自动应用
```java
// 在布局算法应用后自动执行边路径优化
EdgePathOptimizer.optimizeAllEdgePaths(nodes, edges);
```

### 2. 独立测试
```java
// 运行测试类
java org.example.EdgePathOptimizerTest
```

## 效果对比

### 改进前
- ❌ 连接线可能穿过节点中心
- ❌ 多条线重叠在同一路径上
- ❌ 视觉混乱，难以跟踪连接关系

### 改进后
- ✅ 连接线智能避开所有节点
- ✅ 多条线保持适当间距，不重叠
- ✅ 清晰的视觉效果，易于理解流程

## 技术特点

### 1. 智能路径选择
- 多种延伸距离尝试
- 水平/垂直优先策略
- 备用路径机制

### 2. 实时冲突检测
- 节点穿越检测
- 路径重叠检测
- 动态调整策略

### 3. 可配置参数
- 安全边距可调
- 分离距离可调
- 路由策略可选

## 性能优化

### 1. 算法复杂度
- 时间复杂度：O(n²) 其中 n 为边的数量
- 空间复杂度：O(n) 用于存储路径信息

### 2. 优化策略
- 早期终止：找到合适路径立即返回
- 渐进式尝试：从简单到复杂的路径策略
- 缓存机制：避免重复计算

## 扩展性

### 1. 新增路径策略
可以轻松添加新的路径计算策略：
```java
// 添加新的路径计算方法
private static List<RoutePoint> calculateCustomPath(...)
```

### 2. 自定义检测规则
可以扩展冲突检测规则：
```java
// 添加新的冲突检测方法
private static boolean hasCustomConflict(...)
```

## 总结

通过这些改进，连接线的质量得到了显著提升：
1. **完全避免节点穿越**：所有连接线都会智能绕过节点
2. **消除线条重叠**：多条线保持适当间距，视觉清晰
3. **提高可读性**：流程图更容易理解和跟踪
4. **保持性能**：优化算法在保证质量的同时维持良好性能

这些改进确保了生成的流程图具有专业的视觉效果和良好的可读性。
