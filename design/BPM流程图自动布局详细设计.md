# BPM流程图自动布局详细设计

## 1. 项目概述

### 1.1 项目背景
当前BPM流程图的JSON数据中，节点坐标是手动设置的，导致流程图布局混乱、不美观，影响用户体验。需要使用Mxgraph的层次布局算法对流程图进行自动排版，实现从上到下的层次化布局。

### 1.2 项目目标
- 实现BPM流程图的自动布局功能
- 使用Mxgraph层次布局算法优化节点位置
- 保持原有节点属性不变，仅更新坐标信息
- 提供一个main方法实现一键布局处理

### 1.3 输入输出规范
- **输入文件**: `atestfile/processModel.json`
- **输出文件**: `processModel_layouted.json`
- **布局方向**: 从上到下（Top-Down）
- **坐标更新**: 仅更新geometry中的x, y坐标

## 2. 技术方案设计

### 2.1 技术选型
- **开发语言**: Java
- **图形库**: JGraphX (Mxgraph的Java实现)
- **JSON处理**: Jackson
- **构建工具**: Maven
- **布局算法**:
  - mxHierarchicalLayout (层次布局，适用于DAG)
  - mxFastOrganicLayout (有机布局，适用于循环图)
  - 智能算法选择

### 2.2 系统架构
```
ProcessModelLayouter (主类)
├── JsonParser (JSON解析器)
├── GraphBuilder (图构建器)
├── CycleDetector (循环检测器) ⭐ 新增
├── LayoutSelector (布局选择器) ⭐ 新增
├── LayoutProcessor (布局处理器)
└── CoordinateUpdater (坐标更新器)
```

### 2.3 核心依赖
```xml
<dependency>
    <groupId>org.tinyjee.jgraphx</groupId>
    <artifactId>jgraphx</artifactId>
    <version>4.2.2</version>
</dependency>
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.15.2</version>
</dependency>
```

## 3. 数据结构设计

### 3.1 节点信息类
```java
public class NodeInfo {
    private String id;
    private String nodeType;
    private double x, y, width, height;
    private JsonNode originalNode;
    // getters and setters
}
```

### 3.2 边信息类
```java
public class EdgeInfo {
    private String id;
    private String sourceId;
    private String targetId;
    private JsonNode originalEdge;
    // getters and setters
}
```

### 3.3 布局配置类
```java
public class LayoutConfig {
    public static final double NODE_SPACING_X = 150.0;
    public static final double NODE_SPACING_Y = 100.0;
    public static final int LAYOUT_ORIENTATION = SwingConstants.NORTH;
    public static final boolean DISABLE_EDGE_STYLE = false;

    // 新增：布局算法选择
    public enum LayoutAlgorithm {
        AUTO,           // 自动选择
        HIERARCHICAL,   // 层次布局
        ORGANIC,        // 有机布局
        CIRCLE          // 圆形布局
    }

    public static final LayoutAlgorithm DEFAULT_ALGORITHM = LayoutAlgorithm.AUTO;
}
```

### 3.4 循环检测器类
```java
public class CycleDetector {
    public boolean hasCycle(Map<String, NodeInfo> nodes, List<EdgeInfo> edges);
    public List<String> findCycles(Map<String, NodeInfo> nodes, List<EdgeInfo> edges);
    private boolean dfsHasCycle(String node, Set<String> visited, Set<String> recStack,
                               Map<String, List<String>> graph);
}
```

## 4. 核心算法设计

### 4.1 增强的算法流程图
```
开始
  ↓
解析JSON文件
  ↓
提取节点和边信息
  ↓
检测图中是否存在循环 ⭐ 新增
  ↓
选择合适的布局算法 ⭐ 新增
  ├─ 无循环 → 层次布局
  └─ 有循环 → 有机布局
  ↓
构建mxGraph图结构
  ↓
应用选定的布局算法
  ↓
获取新的节点坐标
  ↓
更新原JSON中的坐标
  ↓
输出新的JSON文件
  ↓
结束
```

### 4.2 关键算法实现

#### 4.2.1 JSON解析算法
```java
public Map<String, NodeInfo> parseNodes(JsonNode processModel) {
    Map<String, NodeInfo> nodes = new HashMap<>();
    
    processModel.fields().forEachRemaining(entry -> {
        JsonNode node = entry.getValue();
        if (isVertexNode(node)) {
            NodeInfo nodeInfo = createNodeInfo(entry.getKey(), node);
            nodes.put(entry.getKey(), nodeInfo);
        }
    });
    
    return nodes;
}
```

#### 4.2.2 图构建算法
```java
public mxGraph buildGraph(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
    mxGraph graph = new mxGraph();
    Object parent = graph.getDefaultParent();
    
    graph.getModel().beginUpdate();
    try {
        // 添加节点
        Map<String, Object> cellMap = new HashMap<>();
        for (NodeInfo node : nodes.values()) {
            Object cell = graph.insertVertex(parent, node.getId(), 
                node.getId(), node.getX(), node.getY(), 
                node.getWidth(), node.getHeight());
            cellMap.put(node.getId(), cell);
        }
        
        // 添加边
        for (EdgeInfo edge : edges) {
            Object source = cellMap.get(edge.getSourceId());
            Object target = cellMap.get(edge.getTargetId());
            if (source != null && target != null) {
                graph.insertEdge(parent, edge.getId(), "", source, target);
            }
        }
    } finally {
        graph.getModel().endUpdate();
    }
    
    return graph;
}
```

#### 4.2.3 循环检测算法
```java
public class CycleDetector {
    public boolean hasCycle(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        // 构建邻接表
        Map<String, List<String>> graph = buildAdjacencyList(nodes, edges);

        Set<String> visited = new HashSet<>();
        Set<String> recStack = new HashSet<>();

        // 对每个节点进行DFS检测
        for (String node : graph.keySet()) {
            if (!visited.contains(node)) {
                if (dfsHasCycle(node, visited, recStack, graph)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean dfsHasCycle(String node, Set<String> visited,
                               Set<String> recStack, Map<String, List<String>> graph) {
        visited.add(node);
        recStack.add(node);

        List<String> neighbors = graph.getOrDefault(node, new ArrayList<>());
        for (String neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                if (dfsHasCycle(neighbor, visited, recStack, graph)) {
                    return true;
                }
            } else if (recStack.contains(neighbor)) {
                return true; // 发现循环
            }
        }

        recStack.remove(node);
        return false;
    }
}
```

#### 4.2.4 智能布局选择算法
```java
public class LayoutSelector {
    public LayoutConfig.LayoutAlgorithm selectAlgorithm(
            Map<String, NodeInfo> nodes, List<EdgeInfo> edges,
            LayoutConfig.LayoutAlgorithm userChoice) {

        if (userChoice != LayoutConfig.LayoutAlgorithm.AUTO) {
            return userChoice; // 用户指定算法
        }

        CycleDetector detector = new CycleDetector();
        boolean hasCycle = detector.hasCycle(nodes, edges);

        if (hasCycle) {
            System.out.println("检测到循环引用，使用有机布局算法");
            return LayoutConfig.LayoutAlgorithm.ORGANIC;
        } else {
            System.out.println("未检测到循环，使用层次布局算法");
            return LayoutConfig.LayoutAlgorithm.HIERARCHICAL;
        }
    }
}
```

#### 4.2.5 多算法布局处理
```java
public void applyLayout(mxGraph graph, LayoutConfig.LayoutAlgorithm algorithm) {
    Object parent = graph.getDefaultParent();

    switch (algorithm) {
        case HIERARCHICAL:
            applyHierarchicalLayout(graph, parent);
            break;
        case ORGANIC:
            applyOrganicLayout(graph, parent);
            break;
        case CIRCLE:
            applyCircleLayout(graph, parent);
            break;
        default:
            throw new IllegalArgumentException("不支持的布局算法: " + algorithm);
    }
}

private void applyHierarchicalLayout(mxGraph graph, Object parent) {
    mxHierarchicalLayout layout = new mxHierarchicalLayout(graph, SwingConstants.NORTH);
    layout.setIntraCellSpacing(LayoutConfig.NODE_SPACING_X);
    layout.setInterRankCellSpacing(LayoutConfig.NODE_SPACING_Y);
    layout.setDisableEdgeStyle(LayoutConfig.DISABLE_EDGE_STYLE);
    layout.execute(parent);
}

private void applyOrganicLayout(mxGraph graph, Object parent) {
    mxFastOrganicLayout layout = new mxFastOrganicLayout(graph);
    layout.setForceConstant(120); // 节点间斥力
    layout.setMinDistanceLimit(30); // 最小距离
    layout.setMaxIterations(1000); // 最大迭代次数
    layout.execute(parent);
}

private void applyCircleLayout(mxGraph graph, Object parent) {
    mxCircleLayout layout = new mxCircleLayout(graph);
    layout.setRadius(200); // 圆形半径
    layout.execute(parent);
}
```

## 5. 主类设计

### 5.1 增强的ProcessModelLayouter类
```java
public class ProcessModelLayouter {

    public static void main(String[] args) {
        try {
            // 1. 读取输入文件
            String inputPath = "atestfile/processModel.json";
            String outputPath = "processModel_layouted.json";

            // 2. 解析JSON
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(new File(inputPath));
            JsonNode processModel = root.get("processModel");

            // 3. 提取节点和边信息
            Map<String, NodeInfo> nodes = parseNodes(processModel);
            List<EdgeInfo> edges = parseEdges(processModel);

            System.out.println("解析完成: " + nodes.size() + " 个节点, " + edges.size() + " 条边");

            // 4. 智能选择布局算法 ⭐ 新增
            LayoutSelector selector = new LayoutSelector();
            LayoutConfig.LayoutAlgorithm algorithm = selector.selectAlgorithm(
                nodes, edges, LayoutConfig.DEFAULT_ALGORITHM);

            // 5. 构建图并应用选定的布局算法
            mxGraph graph = buildGraph(nodes, edges);
            applyLayout(graph, algorithm);

            // 6. 更新坐标并输出
            updateCoordinates(graph, nodes);
            generateOutputJson(root, nodes, outputPath);

            System.out.println("布局处理完成，使用算法: " + algorithm);
            System.out.println("输出文件: " + outputPath);

        } catch (Exception e) {
            System.err.println("处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // 支持命令行参数指定布局算法
    public static void main(String[] args) {
        LayoutConfig.LayoutAlgorithm userAlgorithm = LayoutConfig.DEFAULT_ALGORITHM;

        if (args.length > 0) {
            try {
                userAlgorithm = LayoutConfig.LayoutAlgorithm.valueOf(args[0].toUpperCase());
                System.out.println("用户指定布局算法: " + userAlgorithm);
            } catch (IllegalArgumentException e) {
                System.out.println("无效的布局算法参数，使用默认算法: " + userAlgorithm);
            }
        }

        processLayout(userAlgorithm);
    }

    private static void processLayout(LayoutConfig.LayoutAlgorithm userAlgorithm) {
        // 主要处理逻辑...
    }
}
```

## 6. 配置参数说明

### 6.1 布局参数
| 参数名称 | 默认值 | 说明 |
|---------|--------|------|
| NODE_SPACING_X | 150.0 | 节点水平间距(像素) |
| NODE_SPACING_Y | 100.0 | 节点垂直间距(像素) |
| LAYOUT_ORIENTATION | NORTH | 布局方向(从上到下) |
| DISABLE_EDGE_STYLE | false | 是否禁用边样式 |
| DEFAULT_ALGORITHM | AUTO | 默认布局算法选择 |

### 6.2 布局算法选择
| 算法类型 | 适用场景 | 特点 |
|---------|---------|------|
| AUTO | 自动选择 | 根据图结构智能选择最佳算法 |
| HIERARCHICAL | 无循环的DAG | 清晰的层次结构，从上到下 |
| ORGANIC | 有循环的复杂图 | 自然的有机布局，适合复杂关系 |
| CIRCLE | 简单的循环图 | 圆形布局，适合展示循环关系 |

### 6.3 有机布局参数
| 参数名称 | 默认值 | 说明 |
|---------|--------|------|
| FORCE_CONSTANT | 120 | 节点间斥力强度 |
| MIN_DISTANCE_LIMIT | 30 | 节点间最小距离 |
| MAX_ITERATIONS | 1000 | 最大迭代次数 |

### 6.2 节点类型映射
| nodeType | 说明 | 处理方式 |
|----------|------|----------|
| 1 | 开始节点 | 作为根节点处理 |
| 2 | 初始化节点 | 普通节点 |
| 4 | 重新提交节点 | 普通节点 |
| 6 | 审核节点 | 普通节点 |
| 8 | 循环节点 | 普通节点 |
| 9 | 服务节点 | 普通节点 |
| 13 | 网关节点 | 普通节点 |
| 99 | 边/连接线 | 连接关系 |

## 7. 使用方法

### 7.1 编译和运行
```bash
# 编译项目
mvn clean compile

# 自动选择布局算法（推荐）
mvn exec:java -Dexec.mainClass="ProcessModelLayouter"

# 指定布局算法
mvn exec:java -Dexec.mainClass="ProcessModelLayouter" -Dexec.args="HIERARCHICAL"
mvn exec:java -Dexec.mainClass="ProcessModelLayouter" -Dexec.args="ORGANIC"
mvn exec:java -Dexec.mainClass="ProcessModelLayouter" -Dexec.args="CIRCLE"

# 或者直接运行jar包
java -cp target/classes ProcessModelLayouter
java -cp target/classes ProcessModelLayouter ORGANIC
```

### 7.2 运行示例输出
```
解析完成: 45 个节点, 67 条边
检测到循环引用，使用有机布局算法
布局处理完成，使用算法: ORGANIC
输出文件: processModel_layouted.json
```

### 7.2 输入文件要求
- JSON格式正确
- 包含processModel根节点
- 节点必须包含geometry信息
- 边节点必须包含source和target信息

## 8. 注意事项和限制

### 8.1 注意事项
1. 确保输入JSON格式正确
2. 节点ID必须唯一
3. 边的source和target必须指向有效节点
4. 保持原有节点尺寸不变

### 8.2 限制条件
1. 仅支持有向图布局
2. ~~不支持循环引用的复杂图结构~~ **已支持循环引用**
3. 节点数量建议不超过1000个
4. 布局结果可能需要微调

### 8.3 扩展性
- 可以添加其他布局算法支持
- 可以增加布局参数的配置文件
- 可以添加图形预览功能
- 可以支持批量处理多个文件

## 9. 测试方案

### 9.1 单元测试
- JSON解析功能测试
- 图构建功能测试
- 布局算法测试
- 坐标更新测试

### 9.2 集成测试
- 完整流程测试
- 异常情况测试
- 性能测试
- 输出结果验证

### 9.3 测试数据
- 使用提供的processModel.json作为测试数据
- 创建简化的测试用例
- 边界条件测试用例

## 10. 实现细节补充

### 10.1 边信息提取算法
```java
public List<EdgeInfo> parseEdges(JsonNode processModel) {
    List<EdgeInfo> edges = new ArrayList<>();

    processModel.fields().forEachRemaining(entry -> {
        JsonNode node = entry.getValue();
        if (isEdgeNode(node)) {
            EdgeInfo edgeInfo = createEdgeInfo(entry.getKey(), node);
            if (edgeInfo.getSourceId() != null && edgeInfo.getTargetId() != null) {
                edges.add(edgeInfo);
            }
        }
    });

    return edges;
}

private boolean isEdgeNode(JsonNode node) {
    return node.has("edge") && "true".equals(node.get("edge").asText());
}

private boolean isVertexNode(JsonNode node) {
    return node.has("vertex") && "true".equals(node.get("vertex").asText());
}
```

### 10.2 坐标更新算法
```java
public void updateCoordinates(mxGraph graph, Map<String, NodeInfo> nodes) {
    Object parent = graph.getDefaultParent();

    for (int i = 0; i < graph.getModel().getChildCount(parent); i++) {
        Object cell = graph.getModel().getChildAt(parent, i);

        if (graph.getModel().isVertex(cell)) {
            String cellId = (String) graph.getModel().getValue(cell);
            mxGeometry geometry = graph.getModel().getGeometry(cell);

            if (nodes.containsKey(cellId) && geometry != null) {
                NodeInfo nodeInfo = nodes.get(cellId);
                nodeInfo.setX(geometry.getX());
                nodeInfo.setY(geometry.getY());
            }
        }
    }
}
```

### 10.3 JSON输出算法
```java
public void generateOutputJson(JsonNode root, Map<String, NodeInfo> nodes,
                              String outputPath) throws IOException {
    ObjectMapper mapper = new ObjectMapper();
    ObjectNode rootCopy = root.deepCopy();
    ObjectNode processModel = (ObjectNode) rootCopy.get("processModel");

    // 更新节点坐标
    for (Map.Entry<String, NodeInfo> entry : nodes.entrySet()) {
        String nodeId = entry.getKey();
        NodeInfo nodeInfo = entry.getValue();

        if (processModel.has(nodeId)) {
            ObjectNode nodeObj = (ObjectNode) processModel.get(nodeId);
            if (nodeObj.has("geometry")) {
                ObjectNode geometry = (ObjectNode) nodeObj.get("geometry");
                geometry.put("x", (int) nodeInfo.getX());
                geometry.put("y", (int) nodeInfo.getY());
            }
        }
    }

    // 写入输出文件
    mapper.writerWithDefaultPrettyPrinter()
          .writeValue(new File(outputPath), rootCopy);
}
```

## 11. 错误处理和异常管理

### 11.1 异常类型
- `JsonProcessingException`: JSON解析异常
- `IOException`: 文件读写异常
- `IllegalArgumentException`: 参数异常
- `RuntimeException`: 图构建异常

### 11.2 异常处理策略
```java
public class ProcessModelLayouter {
    private static final Logger logger = LoggerFactory.getLogger(ProcessModelLayouter.class);

    public static void main(String[] args) {
        try {
            processLayout();
        } catch (JsonProcessingException e) {
            logger.error("JSON解析失败: {}", e.getMessage());
            System.exit(1);
        } catch (IOException e) {
            logger.error("文件操作失败: {}", e.getMessage());
            System.exit(1);
        } catch (Exception e) {
            logger.error("处理过程中发生未知错误: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
}
```

## 12. 性能优化建议

### 12.1 内存优化
- 使用流式JSON解析处理大文件
- 及时释放不需要的对象引用
- 合理设置JVM堆内存大小

### 12.2 算法优化
- 预先过滤无效节点和边
- 使用并行处理提高解析速度
- 缓存重复计算结果

### 12.3 配置优化
```java
public class PerformanceConfig {
    public static final int MAX_NODES = 1000;
    public static final int MAX_EDGES = 2000;
    public static final long MAX_PROCESSING_TIME = 30000; // 30秒
}
```

## 13. 部署和运行环境

### 13.1 系统要求
- Java 8 或更高版本
- Maven 3.6 或更高版本
- 内存: 最少512MB，推荐1GB
- 磁盘空间: 100MB

### 13.2 Maven配置
```xml
<properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
</properties>

<build>
    <plugins>
        <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <version>3.1.0</version>
            <configuration>
                <mainClass>ProcessModelLayouter</mainClass>
            </configuration>
        </plugin>
    </plugins>
</build>
```

## 14. 总结

本设计方案提供了一个完整的BPM流程图自动布局解决方案，主要特点：

1. **简单易用**: 只需一个main方法即可完成布局处理
2. **算法先进**: 使用Mxgraph成熟的层次布局算法
3. **保持兼容**: 保持原有JSON结构和属性不变
4. **性能优良**: 支持中等规模流程图的快速处理
5. **扩展性强**: 易于添加新的布局算法和配置选项

通过本方案的实施，可以显著改善BPM流程图的视觉效果，提升用户体验，为后续的流程图编辑和展示提供良好的基础。
