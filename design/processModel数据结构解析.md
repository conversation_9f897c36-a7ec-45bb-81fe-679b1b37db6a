# ProcessModel.json 数据结构详细解析

## 概述

`processModel.json` 是用于 Mxgraph 渲染的流程模型数据文件，包含了完整的工作流程图信息，包括节点定义、连线关系、样式配置和位置信息。

## 整体结构

```json
{
    "processModel": {
        "节点ID1": { /* 节点对象 */ },
        "节点ID2": { /* 节点对象 */ },
        "连线ID": { /* 连线对象 */ }
    }
}
```

## 字段详细说明

### 1. 基础标识字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `id` | String | 节点/连线的唯一标识符 | "start", "init", "2" |
| `mxObjectId` | String | Mxgraph内部对象ID | "mxCell#41" |
| `parent` | String/null | 父节点ID，顶级节点为null | null |
| `children` | Array/null | 子节点列表，通常为null | null |

### 2. 节点类型分类

| nodeType | 类型名称 | 说明 | 典型节点 |
|----------|----------|------|----------|
| 1 | 开始节点 | 流程起始点 | start |
| 2 | 初始化节点 | 流程初始化 | init |
| 4 | 重新提交节点 | 回退重做 | recall |
| 6 | 审核节点 | 人工审核任务 | 各种审核节点 |
| 8 | 循环节点 | 循环处理逻辑 | 循环节点 |
| 9 | 服务节点 | 自动化服务 | 各种服务节点 |
| 13 | 网关节点 | 条件分支 | 网关节点 |
| 99 | 连线 | 节点间连接 | 所有连线 |

### 3. 视觉样式字段

#### style 字段
包含CSS样式属性的字符串，控制节点外观：

```css
shadow=0;rounded=1;arcSize=20;rotatable=1;verticalAlign=top;align=left;
overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;
```

常用样式属性：
- `fillColor`: 填充颜色
- `strokeColor`: 边框颜色
- `fontColor`: 字体颜色
- `rounded`: 圆角设置
- `fontSize`: 字体大小
- `fontFamily`: 字体族

#### value 字段
节点显示的HTML内容，包含：
- 节点标题
- 图标背景
- 描述信息
- CSS类名

### 4. 几何位置字段 (geometry)

```json
"geometry": {
    "x": 214,           // X坐标位置
    "y": 276,           // Y坐标位置  
    "width": 150,       // 节点宽度
    "height": 40,       // 节点高度
    "relative": 1,      // 相对定位标识
    "TRANSLATE_CONTROL_POINTS": "true"  // 控制点转换
}
```

### 5. 连线特有字段

#### edge 字段
- 值为 "true" 表示这是一条连线

#### source 和 target 字段
包含完整的起始节点和目标节点对象信息

#### abspoints 字段详解

`abspoints` 是连线路径的关键字段，包含连线经过的所有坐标点数组。

**四个坐标点的含义：**

以实际例子说明：
```json
"abspoints": [
    { "x": 1536, "y": 1146 },  // 点1: 起始点
    { "x": 1536, "y": 1221 },  // 点2: 第一转折点
    { "x": 1617, "y": 1221 },  // 点3: 第二转折点  
    { "x": 1617, "y": 1296 }   // 点4: 终点
]
```

**坐标点路径分析：**

1. **起始点 (1536, 1146)**
   - 连线从源节点的连接点开始
   - 通常位于节点的边缘中心

2. **第一转折点 (1536, 1221)**
   - X坐标保持不变 (1536)
   - Y坐标向下移动 (1146 → 1221)
   - 形成垂直向下的线段

3. **第二转折点 (1617, 1221)**
   - Y坐标保持不变 (1221)
   - X坐标向右移动 (1536 → 1617)
   - 形成水平向右的线段

4. **终点 (1617, 1296)**
   - X坐标保持不变 (1617)
   - Y坐标向下移动 (1221 → 1296)
   - 连接到目标节点

**连线路径特点：**
- 采用正交连线方式（直角转弯）
- 避免斜线连接，保持图形整洁
- 路径为：垂直 → 水平 → 垂直
- 形成类似"Z"字形或"L"字形路径

### 6. 业务逻辑字段

| 字段名 | 说明 | 示例 |
|--------|------|------|
| `nodeName` | 节点业务名称 | "流程初始化" |
| `connectable` | 是否可连接 | "true" |
| `edges` | 相关连线 | null |
| `vertex` | 是否为顶点 | "true" |

## 典型节点示例

### 开始节点
```json
"start": {
    "id": "start",
    "nodeType": 1,
    "style": "fillColor=#1DCC8F;strokeColor=white;fontColor=#FFFFFF;...",
    "value": "<p>Start</p>",
    "geometry": {
        "x": 214, "y": 276,
        "width": 150, "height": 40
    }
}
```

### 网关节点
```json
"网关ID": {
    "nodeType": 13,
    "value": "<div class=\"k2-node-gateway\">...</div>",
    "geometry": { "x": 2567, "y": 7336, "width": 180, "height": 110 }
}
```

### 连线对象
```json
"连线ID": {
    "edge": "true",
    "nodeType": 99,
    "source": { /* 完整源节点对象 */ },
    "target": { /* 完整目标节点对象 */ },
    "geometry": {
        "abspoints": [
            { "x": 起始X, "y": 起始Y },
            { "x": 转折X1, "y": 转折Y1 },
            { "x": 转折X2, "y": 转折Y2 },
            { "x": 终点X, "y": 终点Y }
        ]
    }
}
```

## 渲染用途

1. **流程图绘制**: 在前端使用Mxgraph库渲染完整流程图
2. **节点定位**: 通过geometry精确定位每个节点
3. **连线绘制**: 通过abspoints绘制节点间连接线
4. **样式控制**: 通过style控制视觉效果
5. **交互支持**: 支持拖拽、编辑等操作

## 坐标系统

- 采用笛卡尔坐标系
- 原点(0,0)位于画布左上角
- X轴向右为正方向
- Y轴向下为正方向
- 单位为像素(px)

## 注意事项

1. 连线的abspoints数组长度可变，不一定只有4个点
2. 复杂连线可能包含更多转折点
3. 直线连接可能只有2个点（起点和终点）
4. 坐标值为绝对位置，不是相对偏移
