# 横向分布算法修改说明

## 修改概述

已成功修改 `ProcessModelHierarchicalLayout_Fixed.sql` 文件中的X坐标计算逻辑，实现了同一层级内节点的智能横向分布，确保单节点居中显示，多节点水平均匀分布且居中对齐。

## 核心算法逻辑

### 修改前的问题
```sql
-- 原始逻辑：所有节点都在同一X坐标，导致重叠
n.x_coord = 100 + (n.level_num - 1) * 300
```

**问题**：
- 同一层级的所有节点都有相同的X坐标
- 多节点层级会导致节点完全重叠
- 无法区分单节点和多节点的布局需求

### 修改后的解决方案

```sql
-- 新逻辑：区分单节点和多节点的横向分布
n.x_coord = CASE
    WHEN lc.node_count = 1 THEN
        100 + (n.level_num - 1) * 300                    -- 单节点居中
    ELSE
        -- 多节点水平居中分布
        100 + (n.level_num - 1) * 300                    -- 层级基础X坐标
        - ((lc.node_count - 1) * 120) / 2                -- 起始偏移（居中）
        + (n.level_order - 1) * 120                      -- 节点间隔偏移
END
```

## 算法参数说明

### 1. 基础参数
- **层级基础间隔**: 300像素（层级间的水平间距）
- **节点间隔**: 120像素（同层级内节点间的水平间距）
- **起始X坐标**: 100像素（第一层的基础位置）

### 2. 计算公式详解

#### 单节点层级（node_count = 1）
```
X坐标 = 100 + (level_num - 1) × 300
```

#### 多节点层级（node_count > 1）
```
层级基础X坐标 = 100 + (level_num - 1) × 300
总宽度 = (节点数量 - 1) × 120
起始偏移 = -总宽度 / 2
节点X坐标 = 层级基础X坐标 + 起始偏移 + (level_order - 1) × 120
```

## 布局效果示例

### 示例1：混合层级布局
假设有以下节点分布：
- **第1层**: 1个节点 (开始节点)
- **第2层**: 3个节点 (任务A, 任务B, 任务C)
- **第3层**: 1个节点 (结束节点)

#### 计算过程

**第1层（单节点）**：
```
开始节点: X = 100 + (1-1) × 300 = 100
```

**第2层（3个节点）**：
```
层级基础X坐标 = 100 + (2-1) × 300 = 400
总宽度 = (3-1) × 120 = 240
起始偏移 = -240/2 = -120

任务A (level_order=1): X = 400 + (-120) + (1-1) × 120 = 280
任务B (level_order=2): X = 400 + (-120) + (2-1) × 120 = 400
任务C (level_order=3): X = 400 + (-120) + (3-1) × 120 = 520
```

**第3层（单节点）**：
```
结束节点: X = 100 + (3-1) × 300 = 700
```

#### 最终坐标分配
```
层级 | 节点     | X坐标 | Y坐标 | 布局说明
-----|----------|-------|-------|------------------
1    | 开始节点 | 100   | 100   | 单节点居中
2    | 任务A    | 280   | 300   | 多节点左侧
2    | 任务B    | 400   | 380   | 多节点中心（基础位置）
2    | 任务C    | 520   | 460   | 多节点右侧
3    | 结束节点 | 700   | 500   | 单节点居中
```

### 示例2：不同节点数量的层级
```
层级 | 节点数 | 基础X坐标 | X坐标分布                    | 说明
-----|--------|-----------|------------------------------|------------------
1    | 1      | 100       | [100]                        | 单节点居中
2    | 2      | 400       | [340, 460]                   | 2节点居中分布
3    | 4      | 700       | [520, 640, 760, 880]        | 4节点居中分布
4    | 1      | 1000      | [1000]                       | 单节点居中
```

## 技术特点

### 优势
1. **智能布局**: 自动区分单节点和多节点的布局需求
2. **居中对齐**: 多节点在层级基础位置上居中分布
3. **避免重叠**: 确保同层级内节点有足够的水平间距
4. **视觉平衡**: 保持整体布局的对称性和美观性
5. **可扩展性**: 支持任意数量的节点在同一层级内分布

### 设计考虑
1. **节点宽度**: 基于100像素节点宽度设计
2. **间距设计**: 120像素间距 = 100像素节点 + 20像素间隙
3. **居中算法**: 使用起始偏移确保多节点组合居中
4. **层级间隔**: 保持300像素的层级基础间隔

## 验证功能

### 1. 布局结果显示
- 显示每层级的X坐标范围和分布方式
- 提供详细的节点坐标列表
- 区分单节点和多节点的布局方式

### 2. 横向分布验证
- 验证单节点是否正确居中
- 检查多节点是否无重叠
- 确认多节点组合是否居中对齐
- 提供详细的X坐标验证信息

## 参数调整指南

### 调整节点间隔
```sql
-- 当前: 120像素间隔
+ (n.level_order - 1) * 120

-- 紧凑布局: 100像素间隔
+ (n.level_order - 1) * 100

-- 宽松布局: 150像素间隔
+ (n.level_order - 1) * 150
```

### 调整层级间隔
```sql
-- 当前: 300像素层级间隔
100 + (n.level_num - 1) * 300

-- 紧凑布局: 250像素层级间隔
100 + (n.level_num - 1) * 250

-- 宽松布局: 400像素层级间隔
100 + (n.level_num - 1) * 400
```

### 调整起始位置
```sql
-- 当前: X=100起始
100 + (n.level_num - 1) * 300

-- 左对齐: X=50起始
50 + (n.level_num - 1) * 300

-- 右偏移: X=150起始
150 + (n.level_num - 1) * 300
```

## 预期效果

修改后的横向分布算法将实现：
- ✅ 单节点层级在基础位置居中显示
- ✅ 多节点层级水平均匀分布且整体居中
- ✅ 同层级内节点不重叠，有适当间距
- ✅ 不同层级间保持明显的水平间隔
- ✅ 整体布局对称美观，视觉层次清晰
- ✅ 支持任意数量节点的层级布局
