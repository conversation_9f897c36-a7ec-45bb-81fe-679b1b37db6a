-- 测试重新提交节点定位功能
-- 此脚本用于验证重新提交节点是否正确定位到byStart节点右侧1000像素位置

-- 创建测试存储过程
DELIMITER $$

DROP PROCEDURE IF EXISTS TestRecallPositioning$$

CREATE PROCEDURE TestRecallPositioning(
    IN p_process_id VARCHAR(100)
)
BEGIN
    -- 错误处理
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 输出测试开始信息
    SELECT CONCAT('=== 开始测试重新提交节点定位功能，流程ID: ', p_process_id, ' ===') AS 测试状态;

    -- 检查流程是否存在
    IF NOT EXISTS (SELECT 1 FROM com_paasit_pai_core_processDesignerObj WHERE PId = p_process_id) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '指定的流程ID不存在';
    END IF;

    -- 1. 查看修改前的节点坐标
    SELECT 
        '修改前节点坐标' AS 状态,
        JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.id')) AS 节点ID,
        JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.nodeName')) AS 节点名称,
        CAST(JSON_EXTRACT(node_data, '$.nodeType') AS UNSIGNED) AS 节点类型,
        CAST(JSON_EXTRACT(node_data, '$.geometry.x') AS SIGNED) AS X坐标,
        CAST(JSON_EXTRACT(node_data, '$.geometry.y') AS SIGNED) AS Y坐标
    FROM com_paasit_pai_core_processDesignerObj,
         JSON_TABLE(
             processDesignerData,
             '$.*' COLUMNS (node_data JSON PATH '$')
         ) AS jt
    WHERE PId = p_process_id
      AND JSON_EXTRACT(node_data, '$.vertex') = 'true'
      AND (JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.id')) = 'byStart' 
           OR CAST(JSON_EXTRACT(node_data, '$.nodeType') AS UNSIGNED) = 4)
    ORDER BY JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.id'));

    -- 2. 执行布局算法（调用主存储过程的核心逻辑）
    -- 这里我们需要包含主存储过程的核心逻辑
    -- 为了测试，我们创建一个简化版本

    -- 创建临时表
    DROP TEMPORARY TABLE IF EXISTS tmp_test_nodes;
    CREATE TEMPORARY TABLE tmp_test_nodes (
        id VARCHAR(100) PRIMARY KEY,
        nodeType INT,
        nodeName VARCHAR(500),
        x_coord INT DEFAULT 0,
        y_coord INT DEFAULT 0,
        original_x INT DEFAULT 0,
        original_y INT DEFAULT 0
    ) ENGINE=InnoDB;

    -- 解析节点信息
    INSERT INTO tmp_test_nodes (id, nodeType, nodeName, original_x, original_y)
    SELECT
        JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.id')) AS id,
        CAST(JSON_EXTRACT(node_data, '$.nodeType') AS UNSIGNED) AS nodeType,
        COALESCE(JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.nodeName')), '') AS nodeName,
        COALESCE(CAST(JSON_EXTRACT(node_data, '$.geometry.x') AS SIGNED), 0) AS original_x,
        COALESCE(CAST(JSON_EXTRACT(node_data, '$.geometry.y') AS SIGNED), 0) AS original_y
    FROM com_paasit_pai_core_processDesignerObj,
         JSON_TABLE(
             processDesignerData,
             '$.*' COLUMNS (node_data JSON PATH '$')
         ) AS jt
    WHERE PId = p_process_id
      AND JSON_EXTRACT(node_data, '$.vertex') = 'true'
      AND JSON_EXTRACT(node_data, '$.nodeType') IS NOT NULL;

    -- 3. 应用重新提交节点定位逻辑
    UPDATE tmp_test_nodes recall_node
    SET 
        recall_node.x_coord = COALESCE((
            SELECT bystart_node.original_x + 1000 
            FROM tmp_test_nodes bystart_node 
            WHERE bystart_node.id = 'byStart' 
            LIMIT 1
        ), 3600),
        recall_node.y_coord = COALESCE((
            SELECT bystart_node.original_y 
            FROM tmp_test_nodes bystart_node 
            WHERE bystart_node.id = 'byStart' 
            LIMIT 1
        ), 940)
    WHERE recall_node.nodeType = 4;

    -- 4. 显示计算结果
    SELECT 
        '计算后节点坐标' AS 状态,
        id AS 节点ID,
        nodeName AS 节点名称,
        nodeType AS 节点类型,
        CASE 
            WHEN nodeType = 4 THEN x_coord
            ELSE original_x
        END AS X坐标,
        CASE 
            WHEN nodeType = 4 THEN y_coord
            ELSE original_y
        END AS Y坐标,
        CASE 
            WHEN nodeType = 4 THEN CONCAT('原坐标: (', original_x, ',', original_y, ') -> 新坐标: (', x_coord, ',', y_coord, ')')
            ELSE '未修改'
        END AS 坐标变化
    FROM tmp_test_nodes
    WHERE id = 'byStart' OR nodeType = 4
    ORDER BY nodeType, id;

    -- 5. 验证定位是否正确
    SELECT 
        '定位验证结果' AS 验证项目,
        (SELECT original_x FROM tmp_test_nodes WHERE id = 'byStart' LIMIT 1) AS byStart原始X坐标,
        (SELECT original_y FROM tmp_test_nodes WHERE id = 'byStart' LIMIT 1) AS byStart原始Y坐标,
        (SELECT x_coord FROM tmp_test_nodes WHERE nodeType = 4 LIMIT 1) AS 重新提交新X坐标,
        (SELECT y_coord FROM tmp_test_nodes WHERE nodeType = 4 LIMIT 1) AS 重新提交新Y坐标,
        CASE 
            WHEN (SELECT x_coord FROM tmp_test_nodes WHERE nodeType = 4 LIMIT 1) = 
                 (SELECT original_x + 1000 FROM tmp_test_nodes WHERE id = 'byStart' LIMIT 1)
             AND (SELECT y_coord FROM tmp_test_nodes WHERE nodeType = 4 LIMIT 1) = 
                 (SELECT original_y FROM tmp_test_nodes WHERE id = 'byStart' LIMIT 1)
            THEN '✓ 定位正确：重新提交节点位于byStart节点右侧1000像素，Y坐标相同'
            ELSE '✗ 定位异常：坐标计算不符合预期'
        END AS 验证结果;

    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS tmp_test_nodes;

    COMMIT;

    SELECT '=== 重新提交节点定位功能测试完成 ===' AS 测试状态;

END$$

DELIMITER ;

-- 使用示例
-- CALL TestRecallPositioning('your_process_id_here');
