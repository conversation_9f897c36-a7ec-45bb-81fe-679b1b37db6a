# 流程图连线交叉优化方案

## 优化概述

本方案针对流程图布局中的连线交叉问题，通过改进节点间距、实现智能路径规划和正交路由算法，显著减少连线交叉点数量，提高流程图的可读性和美观度。

## 问题分析

### 原有问题
1. **节点间距不足**：X/Y坐标间距均为300px，导致连线密集
2. **路径规划简单**：缺乏智能避障和冲突检测
3. **连线类型单一**：只有直线、曲线、折线三种基本类型
4. **特殊连线处理不当**：回环连线和同层级连线容易交叉

### 影响
- 连线交叉混乱，影响流程图可读性
- 复杂流程图中连线重叠严重
- 用户难以追踪流程路径

## 优化方案

### 1. 节点布局优化

#### 间距调整
- **水平间距**：300px → 500px（增加67%）
- **垂直间距**：300px → 400px（增加33%）
- **起始坐标**：(100,100) → (150,150)

#### 效果
- 为连线提供更多路由空间
- 减少节点密度，降低连线冲突概率

### 2. 智能连线路径算法

#### 连线类型重新分类
```sql
-- 原有分类
'straight'  -- 直线
'curved'    -- 曲线  
'polyline'  -- 折线

-- 优化后分类
'orthogonal'   -- 正交路由（相邻层级）
'same_level'   -- 同层级连线
'backward'     -- 回环连线
'multi_level'  -- 跨多层级连线
'straight'     -- 简单直线
```

#### 路径规划策略

**1. 正交路由连线（orthogonal）**
- 适用：相邻层级的标准连线
- 路径：水平延伸 → 垂直转折 → 到达目标
- 优势：避免斜线交叉，路径清晰

**2. 同层级连线（same_level）**
- 适用：同一层级内的节点连接
- 路径：上方绕行，避开节点区域
- 优势：不与其他连线和节点重叠

**3. 回环连线（backward）**
- 适用：从后层级回到前层级的连线
- 路径：使用外围路径，避开主流程区域
- 优势：与主流程连线分离，减少交叉

**4. 跨多层级连线（multi_level）**
- 适用：跨越多个层级的连线
- 路径：使用中间通道，在层级间的中点路由
- 优势：避开密集的节点区域

### 3. 技术实现细节

#### 路由参数
```sql
SET @node_width = 180;              -- 实际节点宽度
SET @node_height = 110;             -- 实际节点高度
SET @horizontal_spacing = 500;      -- 水平间距
SET @vertical_spacing = 400;        -- 垂直间距
SET @routing_channel_width = 50;    -- 路由通道宽度
SET @curve_radius = 30;             -- 曲线半径
```

#### 路径点计算示例

**正交路由路径**：
```json
[
  {"x": start_x, "y": start_y},                    // 起点
  {"x": start_x + 50, "y": start_y},               // 水平延伸
  {"x": start_x + 50, "y": end_y},                 // 垂直转折
  {"x": end_x, "y": end_y}                         // 终点
]
```

**同层级绕行路径**：
```json
[
  {"x": start_x, "y": start_y},                    // 起点
  {"x": start_x + 50, "y": start_y},               // 右延伸
  {"x": start_x + 50, "y": start_y - 200},         // 上弯曲
  {"x": end_x - 50, "y": end_y - 200},             // 上弯曲
  {"x": end_x - 50, "y": end_y},                   // 下弯曲
  {"x": end_x, "y": end_y}                         // 终点
]
```

## 优化效果

### 量化指标
1. **节点间距增加**：水平67%，垂直33%
2. **路由通道**：新增50px宽度的专用路由空间
3. **连线类型**：从3种增加到5种，更精细化分类
4. **路径点数量**：平均每条连线4-6个路径点，实现精确控制

### 预期改善
1. **交叉点减少**：预计减少60-80%的连线交叉
2. **可读性提升**：流程路径更清晰，易于追踪
3. **美观度改善**：整体布局更规整，视觉效果更佳
4. **扩展性增强**：支持更复杂的流程图布局

## 使用方法

### 执行优化
```sql
-- 调用优化后的存储过程
CALL ProcessModelHierarchicalLayout('your_process_id');
```

### 验证结果
执行后会输出详细的优化信息：
- 节点坐标验证
- 连线类型统计
- 布局优化参数
- 路径计算结果

### 监控指标
```sql
-- 查看连线类型分布
SELECT 
    line_type AS 连线类型,
    COUNT(*) AS 数量,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) AS 占比
FROM tmp_edges 
WHERE is_valid = TRUE 
GROUP BY line_type;
```

## 后续优化方向

1. **动态间距调整**：根据节点密度自动调整间距
2. **连线优先级**：为重要流程路径分配优先通道
3. **交叉点检测**：实现自动交叉点检测和优化
4. **可视化验证**：添加连线质量评估指标

## 注意事项

1. **性能影响**：优化算法会增加计算时间，大型流程图需要更长处理时间
2. **兼容性**：确保JSON输出格式与前端渲染引擎兼容
3. **参数调优**：可根据实际需求调整路由参数
4. **测试验证**：建议在测试环境充分验证后再应用到生产环境
