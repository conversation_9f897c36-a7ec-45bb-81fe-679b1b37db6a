# 层次化布局算法修改说明

## 修改概述

已成功修改 `ProcessModelHierarchicalLayout_Fixed.sql` 文件，实现了按层级分组的层次化布局算法，确保同一层级的节点在垂直方向对齐，不同层级在水平方向有明显间隔。

## 核心算法逻辑

### 1. 层级计算逻辑（已保持不变）
- **起始点**: 从开始节点（nodeType = 1）开始，设置其 level = 1
- **递归计算**: 通过连线的 source_id 和 target_id 关系，循环计算每个节点的层级
- **层级递增**: 下一层级的节点 level = 当前层级 + 1
- **排除recall节点**: 在第187行明确排除 nodeType != 4 的节点，避免死循环
- **去重处理**: 对于有多个入口的节点，保留最小的 level 值（第199行 LEAST 函数）

### 2. 坐标计算规则（已修改）

#### X坐标计算（水平布局）
```sql
n.x_coord = 100 + (n.level_num - 1) * 300
```
- **第1层**: X = 100 + (1-1) × 300 = 100
- **第2层**: X = 100 + (2-1) × 300 = 400  
- **第3层**: X = 100 + (3-1) × 300 = 700
- **层级间隔**: 300像素

#### Y坐标计算（垂直布局）- 已修复
```sql
n.y_coord = CASE
    WHEN lc.node_count = 1 THEN
        100 + (n.level_num - 1) * 200                 -- 单节点：基于level计算基础位置
    ELSE
        100 + (n.level_num - 1) * 200 + (n.level_order - 1) * 80  -- 多节点：基础位置 + 节点内偏移
END
```

**布局规则**:
- **起始Y坐标**: 100像素
- **层级间隔**: 200像素（每层之间的基础间隔）
- **节点间隔**: 80像素（同层级内节点之间的间隔）

**计算公式**:
- **单节点层级**: Y = 100 + (level_num - 1) × 200
- **多节点层级**: Y = 100 + (level_num - 1) × 200 + (level_order - 1) × 80

### 3. 布局效果示例（修复后）

假设有以下节点分布：
- **第1层**: 1个节点 (开始节点)
- **第2层**: 3个节点 (任务A, 任务B, 任务C)
- **第3层**: 1个节点 (结束节点)

**坐标分配结果**:
```
层级 | 节点     | X坐标 | Y坐标 | 计算过程                           | 说明
-----|----------|-------|-------|-----------------------------------|------------------
1    | 开始节点 | 100   | 100   | 100+(1-1)×200 = 100             | 单节点，第1层基础位置
2    | 任务A    | 400   | 300   | 100+(2-1)×200+(1-1)×80 = 300    | 多节点第1个，第2层
2    | 任务B    | 400   | 380   | 100+(2-1)×200+(2-1)×80 = 380    | 多节点第2个，第2层
2    | 任务C    | 400   | 460   | 100+(2-1)×200+(3-1)×80 = 460    | 多节点第3个，第2层
3    | 结束节点 | 700   | 500   | 100+(3-1)×200 = 500             | 单节点，第3层基础位置
```

**层级排列验证**: ✅ level1(Y=100) < level2(Y=300-460) < level3(Y=500)

## 主要修改内容

### 1. 移除全局排序逻辑
- 删除了 `tmp_global_orders` 临时表
- 移除了全局排序的计算逻辑
- 简化了坐标计算流程

### 2. 恢复层级分组布局
- 使用 `tmp_level_counts` 表统计每层节点数量
- 基于层级内节点数量决定Y坐标布局方式
- 确保同层级节点X坐标相同，Y坐标按排序分布

### 3. 增强调试输出
- 添加层级计算完成状态输出
- 增强层级布局结果显示，包含布局方式说明
- 显示每层级的X坐标验证信息

## 技术特点

### 优势
1. **层级对齐**: 同一层级的所有节点具有相同的X坐标
2. **垂直分布**: 层级内多个节点在Y轴上均匀分布
3. **居中优化**: 单节点层级自动居中显示
4. **避免重叠**: 不同层级和层级内节点都有明确的间隔
5. **稳定排序**: 使用节点ID确保排序的稳定性

### 兼容性
- 保持了原有的临时表结构
- 连线坐标计算逻辑无需修改
- JSON数据更新逻辑保持不变
- 事务处理机制完整保留

## 参数调整指南

### 调整层级间隔（X坐标）
```sql
-- 当前: 300像素间隔
n.x_coord = 100 + (n.level_num - 1) * 300

-- 紧凑布局: 200像素间隔
n.x_coord = 100 + (n.level_num - 1) * 200

-- 宽松布局: 400像素间隔  
n.x_coord = 100 + (n.level_num - 1) * 400
```

### 调整节点间隔（Y坐标）
```sql
-- 当前: 200像素间隔
ELSE 200 * n.level_order

-- 紧凑布局: 150像素间隔
ELSE 150 * n.level_order

-- 宽松布局: 250像素间隔
ELSE 250 * n.level_order
```

### 调整居中位置
```sql
-- 当前: Y=280居中
WHEN lc.node_count = 1 THEN 280

-- 更高位置: Y=200居中
WHEN lc.node_count = 1 THEN 200

-- 更低位置: Y=400居中
WHEN lc.node_count = 1 THEN 400
```

## 预期效果

修改后的布局算法将实现：
- ✅ 同一层级的节点垂直对齐（相同X坐标）
- ✅ 不同层级的节点水平间隔明显（300像素间隔）
- ✅ 层级内多个节点垂直分布均匀（200像素间隔）
- ✅ 单节点层级自动居中显示
- ✅ 避免节点重叠和连线冲突
- ✅ 保持清晰的层次结构视觉效果
