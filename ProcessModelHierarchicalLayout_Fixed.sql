BEGIN
    -- ==================== 变量声明区域 ====================
    DECLARE v_error_count INT DEFAULT 0;           -- 错误计数器
DECLARE v_start_node_id VARCHAR(100) DEFAULT NULL;  -- 开始节点ID
DECLARE v_updated_json JSON;                   -- 更新后的JSON数据
DECLARE v_node_count INT DEFAULT 0;            -- 节点总数
DECLARE v_edge_count INT DEFAULT 0;            -- 连线总数

    -- 异常处理：如果发生SQL异常，回滚事务并重新抛出异常
DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
ROLLBACK;
RESIGNAL;
END;

    -- ==================== 开始事务处理 ====================
START TRANSACTION;

-- 输出处理开始信息
SELECT CONCAT('=== 开始处理流程ID: ', p_process_id, ' ===') AS 处理状态;

-- ==================== 1. 输入参数验证 ====================
-- 检查流程ID是否为空
IF p_process_id IS NULL OR p_process_id = '' THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '流程ID不能为空';
END IF;

    -- 检查指定的流程是否存在于数据库中
SELECT COUNT(*) INTO v_error_count
FROM com_paasit_pai_core_processDesignerObj
WHERE PId = p_process_id;

IF v_error_count = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '指定的流程ID不存在';
END IF;

    -- ==================== 2. 创建临时表存储节点信息 ====================
    -- 删除可能存在的临时表
DROP TEMPORARY TABLE IF EXISTS tmp_nodes;
-- 创建节点临时表，用于存储节点信息和计算结果
CREATE TEMPORARY TABLE tmp_nodes (
                                     id VARCHAR(100) PRIMARY KEY,           -- 节点ID
                                     nodeType INT,                          -- 节点类型（1=开始，2=任务，3=结束，4=recall等）
                                     nodeName VARCHAR(500),                 -- 节点名称
                                     source_id VARCHAR(100),                -- 源节点ID（暂未使用）
                                     target_id VARCHAR(100),                -- 目标节点ID（暂未使用）
                                     level_num INT DEFAULT 0,               -- 节点所在层级（从1开始）
                                     level_order INT DEFAULT 0,             -- 节点在同层级中的排序
                                     x_coord INT DEFAULT 0,                 -- 计算后的X坐标
                                     y_coord INT DEFAULT 0,                 -- 计算后的Y坐标
                                     original_x INT DEFAULT 0,              -- 原始X坐标
                                     original_y INT DEFAULT 0               -- 原始Y坐标
) ENGINE=InnoDB;

-- ==================== 3. 创建临时表存储连线信息 ====================
-- 删除可能存在的临时表
DROP TEMPORARY TABLE IF EXISTS tmp_edges;
-- 创建连线临时表，用于存储连线关系和坐标信息
CREATE TEMPORARY TABLE tmp_edges (
                                     id VARCHAR(100) PRIMARY KEY,           -- 连线ID
                                     source_id VARCHAR(100),                -- 源节点ID
                                     target_id VARCHAR(100),                -- 目标节点ID
                                     is_valid BOOLEAN DEFAULT FALSE,        -- 连线是否有效
    -- 连线坐标信息
                                     start_x INT DEFAULT 0,                 -- 起点X坐标（源节点连接点）
                                     start_y INT DEFAULT 0,                 -- 起点Y坐标（源节点连接点）
                                     end_x INT DEFAULT 0,                   -- 终点X坐标（目标节点连接点）
                                     end_y INT DEFAULT 0,                   -- 终点Y坐标（目标节点连接点）
    -- 路径信息
                                     path_points JSON DEFAULT NULL,         -- 中间路径点（JSON数组格式）
                                     line_type VARCHAR(20) DEFAULT 'straight', -- 连线类型：straight(直线), curved(曲线), polyline(折线)
    -- 计算状态
                                     is_calculated BOOLEAN DEFAULT FALSE    -- 坐标是否已计算
) ENGINE=InnoDB;

-- ==================== 4. 解析JSON数据，提取节点信息 ====================
-- 从JSON数据中解析出所有节点信息并插入临时表
INSERT INTO tmp_nodes (id, nodeType, nodeName, original_x, original_y)
SELECT
    JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.id')) AS id,                    -- 提取节点ID
    CAST(JSON_EXTRACT(node_data, '$.nodeType') AS UNSIGNED) AS nodeType,    -- 提取节点类型
    COALESCE(JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.nodeName')), '') AS nodeName,  -- 提取节点名称
    COALESCE(CAST(JSON_EXTRACT(node_data, '$.geometry.x') AS SIGNED), 0) AS original_x,  -- 提取原始X坐标
    COALESCE(CAST(JSON_EXTRACT(node_data, '$.geometry.y') AS SIGNED), 0) AS original_y   -- 提取原始Y坐标
FROM com_paasit_pai_core_processDesignerObj,
     JSON_TABLE(
             processDesignerData,                                               -- 从JSON数据中
             '$.*' COLUMNS (node_data JSON PATH '$')                           -- 提取每个对象
     ) AS jt
WHERE PId = p_process_id                                                   -- 指定流程ID
  AND JSON_EXTRACT(node_data, '$.vertex') = 'true'                        -- 只要顶点（节点）
  AND JSON_EXTRACT(node_data, '$.nodeType') IS NOT NULL;                  -- 节点类型不为空

-- 统计解析到的节点数量
SELECT COUNT(*) INTO v_node_count FROM tmp_nodes;
SELECT CONCAT('解析到 ', v_node_count, ' 个节点') AS 解析状态;

-- 按节点类型统计解析结果
SELECT
    nodeType AS 节点类型,
    CASE
        WHEN nodeType = 1 THEN '开始节点'
        WHEN nodeType = 2 THEN '任务节点'
        WHEN nodeType = 3 THEN '结束节点'
        WHEN nodeType = 4 THEN 'Recall节点'
        ELSE CONCAT('未知类型(', nodeType, ')')
        END AS 类型说明,
    COUNT(*) AS 节点数量,
    GROUP_CONCAT(id ORDER BY id SEPARATOR ', ') AS 节点ID列表
FROM tmp_nodes
GROUP BY nodeType
ORDER BY nodeType;

-- ==================== 5. 解析连线信息（仅用于层级计算，不处理连线坐标） ====================
-- 从JSON数据中解析出所有连线关系并插入临时表
INSERT INTO tmp_edges (id, source_id, target_id, is_valid)
SELECT
    JSON_UNQUOTE(JSON_EXTRACT(edge_data, '$.id')) AS id,                   -- 提取连线ID
    JSON_UNQUOTE(JSON_EXTRACT(edge_data, '$.source.id')) AS source_id,    -- 提取源节点ID
    JSON_UNQUOTE(JSON_EXTRACT(edge_data, '$.target.id')) AS target_id,    -- 提取目标节点ID
    CASE
        WHEN JSON_EXTRACT(edge_data, '$.source.id') IS NOT NULL
            AND JSON_EXTRACT(edge_data, '$.target.id') IS NOT NULL
            THEN TRUE
        ELSE FALSE
        END AS is_valid                                                        -- 判断连线是否有效
FROM com_paasit_pai_core_processDesignerObj,
     JSON_TABLE(
             processDesignerData,                                               -- 从JSON数据中
             '$.*' COLUMNS (edge_data JSON PATH '$')                           -- 提取每个对象
     ) AS jt
WHERE PId = p_process_id                                                   -- 指定流程ID
  AND JSON_EXTRACT(edge_data, '$.edge') = 'true'                          -- 只要边（连线）
  AND JSON_EXTRACT(edge_data, '$.source.id') IS NOT NULL                  -- 源节点不为空
  AND JSON_EXTRACT(edge_data, '$.target.id') IS NOT NULL;                 -- 目标节点不为空

-- 统计解析到的有效连线数量
SELECT COUNT(*) INTO v_edge_count FROM tmp_edges WHERE is_valid = TRUE;
SELECT CONCAT('解析到 ', v_edge_count, ' 条有效连线') AS 解析状态;

-- ==================== 6. 找到开始节点 ====================
-- 查找开始节点（nodeType = 1），作为层级计算的起点
SELECT id INTO v_start_node_id
FROM tmp_nodes
WHERE nodeType = 1                                                         -- 开始节点类型为1
LIMIT 1;

-- 如果没有找到开始节点，抛出异常
IF v_start_node_id IS NULL THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '未找到开始节点 (nodeType = 1)';
END IF;

SELECT CONCAT('找到开始节点: ', v_start_node_id) AS 开始节点;

-- ==================== 7. 使用循环方式计算节点层级（避免递归CTE与临时表的兼容性问题） ====================
-- 这是整个算法的核心：通过循环迭代计算每个节点的层级

-- 创建临时表存储层级计算结果
DROP TEMPORARY TABLE IF EXISTS tmp_node_levels;
CREATE TEMPORARY TABLE tmp_node_levels (
                                           id VARCHAR(100) PRIMARY KEY,
                                           level_num INT,
                                           processed BOOLEAN DEFAULT FALSE
) ENGINE=MEMORY;

-- 初始化：设置开始节点为第1层
UPDATE tmp_nodes SET level_num = 1 WHERE id = v_start_node_id;
INSERT INTO tmp_node_levels (id, level_num, processed) VALUES (v_start_node_id, 1, FALSE);

-- 循环计算层级
BEGIN
    DECLARE v_current_level INT DEFAULT 1;
DECLARE v_nodes_processed INT DEFAULT 0;
DECLARE v_max_iterations INT DEFAULT 20; -- 防止无限循环

level_calculation_loop: WHILE v_current_level <= v_max_iterations DO
-- 创建临时表存储当前层级的节点，完全避免在复杂查询中引用tmp_node_levels
DROP TEMPORARY TABLE IF EXISTS tmp_current_level_nodes;
CREATE TEMPORARY TABLE tmp_current_level_nodes (
                                                   id VARCHAR(100) PRIMARY KEY
) ENGINE=MEMORY;

-- 先获取当前层级未处理的节点
INSERT INTO tmp_current_level_nodes (id)
SELECT id FROM tmp_node_levels
WHERE level_num = v_current_level AND processed = FALSE;

-- 创建临时表存储下一层级的新节点
DROP TEMPORARY TABLE IF EXISTS tmp_next_level_nodes;
CREATE TEMPORARY TABLE tmp_next_level_nodes (
                                                id VARCHAR(100) PRIMARY KEY,
                                                level_num INT
) ENGINE=MEMORY;

-- 找到下一层级的节点（完全避免引用tmp_node_levels）
INSERT INTO tmp_next_level_nodes (id, level_num)
SELECT DISTINCT
    target_node.id,
    v_current_level + 1
FROM tmp_current_level_nodes current_nodes
         INNER JOIN tmp_edges e ON e.source_id = current_nodes.id AND e.is_valid = TRUE
         INNER JOIN tmp_nodes target_node ON target_node.id = e.target_id
WHERE target_node.id != v_start_node_id;  -- 避免回到开始节点（允许recall节点参与层级计算）

-- 移除已经存在的节点（使用单独的查询）
DELETE nln FROM tmp_next_level_nodes nln
                    INNER JOIN tmp_node_levels existing ON existing.id = nln.id;

-- 将新节点插入到层级表中
INSERT INTO tmp_node_levels (id, level_num, processed)
SELECT id, level_num, FALSE
FROM tmp_next_level_nodes
ON DUPLICATE KEY UPDATE
    level_num = LEAST(tmp_node_levels.level_num, VALUES(level_num));  -- 保留最小层级

-- 检查是否有新节点被处理
SELECT ROW_COUNT() INTO v_nodes_processed;

-- 标记当前层级的节点为已处理
UPDATE tmp_node_levels
SET processed = TRUE
WHERE level_num = v_current_level AND processed = FALSE;

-- 如果没有新节点被处理，退出循环
IF v_nodes_processed = 0 THEN
            LEAVE level_calculation_loop;
END IF;

        -- 清理当前迭代的临时表
DROP TEMPORARY TABLE IF EXISTS tmp_current_level_nodes;
DROP TEMPORARY TABLE IF EXISTS tmp_next_level_nodes;

-- 进入下一层级
SET v_current_level = v_current_level + 1;
END WHILE;

    -- 清理循环中使用的临时表
DROP TEMPORARY TABLE IF EXISTS tmp_current_level_nodes;
DROP TEMPORARY TABLE IF EXISTS tmp_next_level_nodes;
END;

-- 将计算出的层级信息更新到节点临时表中
UPDATE tmp_nodes n
    INNER JOIN tmp_node_levels nl ON n.id = nl.id
SET n.level_num = nl.level_num;

-- 输出层级计算结果（调试信息）
SELECT
    '层级计算完成' AS 状态,
    COUNT(DISTINCT level_num) AS 总层级数,
    COUNT(*) AS 已分配层级的节点数,
    MIN(level_num) AS 最小层级,
    MAX(level_num) AS 最大层级
FROM tmp_nodes
WHERE level_num > 0;

-- 按节点类型分析层级分配情况
SELECT
    nodeType AS 节点类型,
    CASE
        WHEN nodeType = 1 THEN '开始节点'
        WHEN nodeType = 2 THEN '任务节点'
        WHEN nodeType = 3 THEN '结束节点'
        WHEN nodeType = 4 THEN 'Recall节点'
        ELSE CONCAT('未知类型(', nodeType, ')')
        END AS 类型说明,
    COUNT(*) AS 节点总数,
    COUNT(CASE WHEN level_num > 0 THEN 1 END) AS 已分配层级数,
    COUNT(CASE WHEN level_num = 0 OR level_num IS NULL THEN 1 END) AS 未分配层级数,
    MIN(level_num) AS 最小层级,
    MAX(level_num) AS 最大层级,
    CASE
        WHEN nodeType = 4 THEN '✓ Recall节点应被排除'
        WHEN COUNT(CASE WHEN level_num > 0 THEN 1 END) = COUNT(*) THEN '✓ 所有节点已分配层级'
        ELSE '✗ 存在未分配层级的节点'
        END AS 分配状态,
    GROUP_CONCAT(CASE WHEN level_num > 0 THEN CONCAT(id, '(L', level_num, ')') END ORDER BY level_num, id SEPARATOR ', ') AS 已分配节点,
    GROUP_CONCAT(CASE WHEN level_num = 0 OR level_num IS NULL THEN id END ORDER BY id SEPARATOR ', ') AS 未分配节点
FROM tmp_nodes
GROUP BY nodeType
ORDER BY nodeType;

-- ==================== 8. 计算每个层级内的节点排序 ====================
-- 为同一层级内的节点分配排序号，用于后续的Y坐标计算
-- 创建临时表存储排序结果，避免在UPDATE中多次引用同一临时表
DROP TEMPORARY TABLE IF EXISTS tmp_node_orders;
CREATE TEMPORARY TABLE tmp_node_orders (
                                           id VARCHAR(100) PRIMARY KEY,
                                           level_order INT
) ENGINE=MEMORY;

-- 计算每个层级内的节点排序
INSERT INTO tmp_node_orders (id, level_order)
SELECT
    id,
    ROW_NUMBER() OVER (PARTITION BY level_num ORDER BY id) as level_order
FROM tmp_nodes
WHERE level_num > 0;

-- 更新节点排序信息
UPDATE tmp_nodes n
    INNER JOIN tmp_node_orders no ON n.id = no.id
SET n.level_order = no.level_order
WHERE n.level_num > 0;

-- ==================== 9. 计算节点坐标（核心布局算法） ====================
-- 使用简化的网格状布局算法，统一的坐标计算公式

-- 根据层级和层级内排序计算每个节点的X、Y坐标（优化的网格状布局）
UPDATE tmp_nodes n
SET
    -- X坐标计算：按level_order从左往右排列，增加间隔到500px以减少连线交叉
    n.x_coord = 150 + (n.level_order - 1) * 500,
    -- Y坐标计算：按level_num从上往下排列，增加间隔到400px以提供更多连线空间
    n.y_coord = 150 + (n.level_num - 1) * 400
WHERE n.level_num > 0;                                                   -- 只处理已分配层级的节点

-- ==================== 9.1. 特殊处理：重新提交节点坐标调整 ====================
-- 将重新提交节点（recall节点，nodeType = 4）定位到与byStart节点相同的水平行，右侧1000像素位置

-- 首先获取byStart节点的坐标
SET @bystart_x = (SELECT x_coord FROM tmp_nodes WHERE id = 'byStart' LIMIT 1);
SET @bystart_y = (SELECT y_coord FROM tmp_nodes WHERE id = 'byStart' LIMIT 1);

-- 如果没有找到byStart节点，使用默认坐标
SET @bystart_x = COALESCE(@bystart_x, 2600);
SET @bystart_y = COALESCE(@bystart_y, 940);

-- 更新重新提交节点的坐标
UPDATE tmp_nodes
SET
    x_coord = @bystart_x + 1000,  -- byStart节点右侧1000像素
    y_coord = @bystart_y          -- 与byStart节点相同的Y坐标
WHERE nodeType = 4;  -- 只处理重新提交节点

-- 输出重新提交节点坐标调整结果
SELECT
    '重新提交节点坐标调整完成' AS 处理状态,
    COUNT(*) AS 处理的节点数量,
    GROUP_CONCAT(CONCAT(id, '(', x_coord, ',', y_coord, ')') SEPARATOR ', ') AS 节点坐标详情
FROM tmp_nodes
WHERE nodeType = 4;

-- 按节点类型分析坐标分配情况
SELECT
    nodeType AS 节点类型,
    CASE
        WHEN nodeType = 1 THEN '开始节点'
        WHEN nodeType = 2 THEN '任务节点'
        WHEN nodeType = 3 THEN '结束节点'
        WHEN nodeType = 4 THEN 'Recall节点'
        ELSE CONCAT('未知类型(', nodeType, ')')
        END AS 类型说明,
    COUNT(*) AS 节点总数,
    COUNT(CASE WHEN level_num > 0 OR nodeType = 4 THEN 1 END) AS 已布局节点数,
    COUNT(CASE WHEN (level_num = 0 OR level_num IS NULL) AND nodeType != 4 THEN 1 END) AS 未布局节点数,
    MIN(CASE WHEN level_num > 0 THEN level_num END) AS 最小层级,
    MAX(CASE WHEN level_num > 0 THEN level_num END) AS 最大层级,
    MIN(CASE WHEN level_num > 0 OR nodeType = 4 THEN x_coord END) AS 最小X坐标,
    MAX(CASE WHEN level_num > 0 OR nodeType = 4 THEN x_coord END) AS 最大X坐标,
    MIN(CASE WHEN level_num > 0 OR nodeType = 4 THEN y_coord END) AS 最小Y坐标,
    MAX(CASE WHEN level_num > 0 OR nodeType = 4 THEN y_coord END) AS 最大Y坐标,
    CASE
        WHEN nodeType = 4 AND COUNT(CASE WHEN x_coord IS NOT NULL AND y_coord IS NOT NULL THEN 1 END) = COUNT(*) THEN '✓ Recall节点特殊布局完成'
        WHEN nodeType != 4 AND COUNT(CASE WHEN level_num > 0 THEN 1 END) = COUNT(*) THEN '✓ 坐标分配正确'
        ELSE '✗ 坐标分配异常'
        END AS 坐标分配状态,
    GROUP_CONCAT(CASE WHEN level_num > 0 OR nodeType = 4 THEN CONCAT(id, '(', x_coord, ',', y_coord, ')') END ORDER BY level_num, level_order SEPARATOR ', ') AS 坐标详情
FROM tmp_nodes
GROUP BY nodeType
ORDER BY nodeType;

-- ==================== 10. 输出层级布局结果（用于调试和查看） ====================
-- 显示每个层级的节点分布情况，验证网格状布局效果
SELECT
    level_num AS 层级,                                                     -- 层级编号
    COUNT(*) AS 节点数量,                                                  -- 该层级的节点数量
    MIN(y_coord) AS Y坐标,                                                 -- 该层级的Y坐标（网格布局中同层级相同）
    100 + (level_num - 1) * 300 AS 预期Y坐标,                             -- 预期的Y坐标
    CASE
        WHEN MIN(y_coord) = MAX(y_coord) AND MIN(y_coord) = 100 + (level_num - 1) * 300
            THEN '✓ Y坐标正确'
        ELSE '✗ Y坐标异常'
        END AS Y坐标验证,                                                       -- Y坐标验证结果
    MIN(x_coord) AS 最小X坐标,                                             -- 该层级的最小X坐标
    MAX(x_coord) AS 最大X坐标,                                             -- 该层级的最大X坐标
    CONCAT('网格布局(Y=', MIN(y_coord), ', X=', MIN(x_coord), '-', MAX(x_coord), ')') AS 布局方式,
    GROUP_CONCAT(CONCAT('节点', level_order, '(', x_coord, ',', y_coord, ')') ORDER BY level_order SEPARATOR ', ') AS 节点坐标  -- 节点坐标列表
FROM tmp_nodes
WHERE level_num > 0                                                       -- 只显示已布局的节点
GROUP BY level_num
ORDER BY level_num;

-- 验证网格布局的正确性
SELECT
    '网格布局验证' AS 验证项目,
    COUNT(DISTINCT level_num) AS 总层级数,
    COUNT(*) AS 总节点数,
    CASE
        WHEN COUNT(DISTINCT CASE WHEN level_num = 1 THEN y_coord END) = 1
            AND COUNT(DISTINCT CASE WHEN level_num = 2 THEN y_coord END) <= 1
            AND (COUNT(DISTINCT CASE WHEN level_num = 2 THEN y_coord END) = 0
                OR MIN(CASE WHEN level_num = 1 THEN y_coord END) < MIN(CASE WHEN level_num = 2 THEN y_coord END))
            THEN '✓ 纵向层级排列正确'
        ELSE '✗ 纵向层级排列有问题'
        END AS 纵向验证结果,
    CASE
        WHEN COUNT(*) = COUNT(DISTINCT CONCAT(x_coord, ',', y_coord)) THEN '✓ 所有节点坐标唯一'
        ELSE '✗ 存在节点坐标重叠'
        END AS 坐标唯一性验证,
    MIN(CASE WHEN level_num = 1 THEN y_coord END) AS Level1_Y坐标,
    MIN(CASE WHEN level_num = 2 THEN y_coord END) AS Level2_Y坐标,
    MIN(CASE WHEN level_num = 3 THEN y_coord END) AS Level3_Y坐标
FROM tmp_nodes
WHERE level_num > 0;

-- 验证优化后的网格坐标计算公式
SELECT
    level_num AS 层级,
    level_order AS 层级内排序,
    x_coord AS 实际X坐标,
    150 + (level_order - 1) * 500 AS 预期X坐标,
    y_coord AS 实际Y坐标,
    150 + (level_num - 1) * 400 AS 预期Y坐标,
    CASE
        WHEN x_coord = 150 + (level_order - 1) * 500
            AND y_coord = 150 + (level_num - 1) * 400
            THEN '✓ 优化坐标计算正确'
        ELSE '✗ 坐标计算错误'
        END AS 坐标验证,
    CONCAT('(', x_coord, ',', y_coord, ')') AS 节点坐标,
    CASE
        WHEN level_order > 1 THEN CONCAT('与前一节点间距: ', x_coord - LAG(x_coord) OVER (PARTITION BY level_num ORDER BY level_order), 'px')
        ELSE '首个节点'
    END AS 水平间距验证
FROM tmp_nodes
WHERE level_num > 0
ORDER BY level_num, level_order;

-- ==================== 11. 计算连线坐标（新增功能） ====================
-- 基于已计算的节点坐标，计算所有有效连线的起点和终点坐标

-- 定义节点尺寸常量和连线优化参数
SET @node_width = 180;                                                    -- 节点宽度（实际节点宽度）
SET @node_height = 110;                                                   -- 节点高度（实际节点高度）
SET @connection_offset = 15;                                              -- 连接点偏移量
SET @horizontal_spacing = 500;                                            -- 水平间距
SET @vertical_spacing = 400;                                              -- 垂直间距
SET @routing_channel_width = 50;                                          -- 路由通道宽度
SET @curve_radius = 30;                                                   -- 曲线半径

-- 计算基础连线坐标：分步处理避免在同一UPDATE中多次引用tmp_nodes
-- 创建临时表存储连线的源节点和目标节点信息
DROP TEMPORARY TABLE IF EXISTS tmp_edge_coordinates;
CREATE TEMPORARY TABLE tmp_edge_coordinates (
                                                edge_id VARCHAR(100) PRIMARY KEY,
                                                source_x INT,
                                                source_y INT,
                                                source_level INT,
                                                target_x INT,
                                                target_y INT,
                                                target_level INT
) ENGINE=MEMORY;

-- 分步收集连线的源节点和目标节点坐标信息，避免在同一查询中多次引用tmp_nodes
-- 先收集源节点信息
INSERT INTO tmp_edge_coordinates (edge_id, source_x, source_y, source_level)
SELECT
    e.id,
    n.x_coord,
    n.y_coord,
    n.level_num
FROM tmp_edges e
         INNER JOIN tmp_nodes n ON e.source_id = n.id AND n.level_num > 0
WHERE e.is_valid = TRUE;

-- 再更新目标节点信息
UPDATE tmp_edge_coordinates ec
    INNER JOIN tmp_edges e ON ec.edge_id = e.id
    INNER JOIN tmp_nodes n ON e.target_id = n.id AND n.level_num > 0
SET
    ec.target_x = n.x_coord,
    ec.target_y = n.y_coord,
    ec.target_level = n.level_num
WHERE e.is_valid = TRUE;

-- 基于收集的坐标信息更新连线坐标（优化的正交路由）
UPDATE tmp_edges e
    INNER JOIN tmp_edge_coordinates ec ON e.id = ec.edge_id
SET
    -- 起点坐标：源节点右边缘中心
    e.start_x = ec.source_x + @node_width,                               -- 节点右边缘
    e.start_y = ec.source_y + (@node_height / 2),                        -- 节点垂直中心
    -- 终点坐标：目标节点左边缘中心
    e.end_x = ec.target_x - @connection_offset,                          -- 节点左边缘（留出连接间隙）
    e.end_y = ec.target_y + (@node_height / 2),                          -- 节点垂直中心
    -- 优化的连线类型分类
    e.line_type = CASE
                      WHEN ABS(ec.source_level - ec.target_level) = 1 AND ABS(ec.source_y - ec.target_y) <= @vertical_spacing
                          THEN 'orthogonal'                                            -- 相邻层级用正交路由
                      WHEN ec.source_level = ec.target_level THEN 'same_level'         -- 同层级连线
                      WHEN ec.source_level > ec.target_level THEN 'backward'           -- 回环连接
                      WHEN ABS(ec.source_level - ec.target_level) > 1 THEN 'multi_level' -- 跨多层级
                      ELSE 'straight'                                                  -- 默认直线
        END,
    e.is_calculated = TRUE;

-- ==================== 12. 智能连线路径优化算法 ====================
-- 实现正交路由和连线冲突避免机制

-- 1. 处理正交路由连线（相邻层级的标准连线）
UPDATE tmp_edges e
    INNER JOIN tmp_edge_coordinates ec ON e.id = ec.edge_id
SET e.path_points = JSON_ARRAY(
        JSON_OBJECT('x', e.start_x, 'y', e.start_y),                          -- 起点
        JSON_OBJECT('x', e.start_x + @routing_channel_width, 'y', e.start_y), -- 水平延伸
        JSON_OBJECT('x', e.start_x + @routing_channel_width, 'y', e.end_y),   -- 垂直转折
        JSON_OBJECT('x', e.end_x, 'y', e.end_y)                              -- 终点
                    )
WHERE e.line_type = 'orthogonal'
  AND e.is_calculated = TRUE;

-- 2. 处理同层级连线：使用上方绕行路径避免节点重叠
UPDATE tmp_edges e
    INNER JOIN tmp_edge_coordinates ec ON e.id = ec.edge_id
SET e.path_points = JSON_ARRAY(
        JSON_OBJECT('x', e.start_x, 'y', e.start_y),                          -- 起点
        JSON_OBJECT('x', e.start_x + @routing_channel_width, 'y', e.start_y), -- 右延伸
        JSON_OBJECT('x', e.start_x + @routing_channel_width, 'y', e.start_y - (@vertical_spacing / 2)), -- 上弯曲
        JSON_OBJECT('x', e.end_x - @routing_channel_width, 'y', e.end_y - (@vertical_spacing / 2)),     -- 上弯曲
        JSON_OBJECT('x', e.end_x - @routing_channel_width, 'y', e.end_y),     -- 下弯曲
        JSON_OBJECT('x', e.end_x, 'y', e.end_y)                              -- 终点
                    )
WHERE e.line_type = 'same_level'
  AND e.is_calculated = TRUE;

-- 3. 处理回环连线：使用外围路径避免与主流程交叉
UPDATE tmp_edges e
    INNER JOIN tmp_edge_coordinates ec ON e.id = ec.edge_id
SET e.path_points = JSON_ARRAY(
        JSON_OBJECT('x', e.start_x, 'y', e.start_y),                          -- 起点
        JSON_OBJECT('x', e.start_x + (@horizontal_spacing / 2), 'y', e.start_y), -- 右延伸
        JSON_OBJECT('x', e.start_x + (@horizontal_spacing / 2), 'y', e.start_y - @vertical_spacing), -- 上弯曲
        JSON_OBJECT('x', e.end_x - (@horizontal_spacing / 2), 'y', e.end_y - @vertical_spacing),     -- 上弯曲
        JSON_OBJECT('x', e.end_x - (@horizontal_spacing / 2), 'y', e.end_y),  -- 下弯曲
        JSON_OBJECT('x', e.end_x, 'y', e.end_y)                              -- 终点
                    )
WHERE e.line_type = 'backward'
  AND e.is_calculated = TRUE;

-- 4. 处理跨多层级连线：使用中间通道路径
UPDATE tmp_edges e
    INNER JOIN tmp_edge_coordinates ec ON e.id = ec.edge_id
SET e.path_points = JSON_ARRAY(
        JSON_OBJECT('x', e.start_x, 'y', e.start_y),                          -- 起点
        JSON_OBJECT('x', e.start_x + @routing_channel_width, 'y', e.start_y), -- 右延伸
        JSON_OBJECT('x', e.start_x + @routing_channel_width, 'y', (ec.source_y + ec.target_y) / 2), -- 中间水平线
        JSON_OBJECT('x', e.end_x - @routing_channel_width, 'y', (ec.source_y + ec.target_y) / 2),   -- 中间水平线
        JSON_OBJECT('x', e.end_x - @routing_channel_width, 'y', e.end_y),     -- 左延伸
        JSON_OBJECT('x', e.end_x, 'y', e.end_y)                              -- 终点
                    )
WHERE e.line_type = 'multi_level'
  AND e.is_calculated = TRUE;

-- 5. 处理直线连线：简单的正交路径
UPDATE tmp_edges e
    INNER JOIN tmp_edge_coordinates ec ON e.id = ec.edge_id
SET e.path_points = JSON_ARRAY(
        JSON_OBJECT('x', e.start_x, 'y', e.start_y),                          -- 起点
        JSON_OBJECT('x', (e.start_x + e.end_x) / 2, 'y', e.start_y),         -- 中点水平
        JSON_OBJECT('x', (e.start_x + e.end_x) / 2, 'y', e.end_y),           -- 中点垂直
        JSON_OBJECT('x', e.end_x, 'y', e.end_y)                              -- 终点
                    )
WHERE e.line_type = 'straight'
  AND e.is_calculated = TRUE;

-- 清理连线坐标临时表
DROP TEMPORARY TABLE IF EXISTS tmp_edge_coordinates;

-- 输出优化后的连线坐标计算结果
SELECT
    '智能连线路径优化完成' AS 状态,
    COUNT(*) AS 总连线数,
    SUM(CASE WHEN is_calculated = TRUE THEN 1 ELSE 0 END) AS 已计算连线数,
    SUM(CASE WHEN line_type = 'orthogonal' THEN 1 ELSE 0 END) AS 正交路由连线数,
    SUM(CASE WHEN line_type = 'same_level' THEN 1 ELSE 0 END) AS 同层级连线数,
    SUM(CASE WHEN line_type = 'backward' THEN 1 ELSE 0 END) AS 回环连线数,
    SUM(CASE WHEN line_type = 'multi_level' THEN 1 ELSE 0 END) AS 跨层级连线数,
    SUM(CASE WHEN line_type = 'straight' THEN 1 ELSE 0 END) AS 直线连线数,
    CONCAT('节点间距优化: 水平', @horizontal_spacing, 'px, 垂直', @vertical_spacing, 'px') AS 布局优化信息
FROM tmp_edges
WHERE is_valid = TRUE;

-- ==================== 13. 更新原始JSON数据中的节点和连线坐标 ====================
-- 获取原始的JSON数据
SELECT processDesignerData INTO v_updated_json
FROM com_paasit_pai_core_processDesignerObj
WHERE PId = p_process_id;

-- 使用游标逐个更新节点坐标到JSON中
BEGIN
    -- 游标相关变量声明
    DECLARE done INT DEFAULT FALSE;                                       -- 游标结束标志
DECLARE v_node_id VARCHAR(100);                                       -- 当前处理的节点ID
DECLARE v_x_coord INT;                                                 -- 当前节点的X坐标
DECLARE v_y_coord INT;                                                 -- 当前节点的Y坐标
DECLARE v_update_count INT DEFAULT 0;                                 -- 更新计数器

        -- 声明游标：遍历所有已布局的节点（包括recall节点）
DECLARE node_cursor CURSOR FOR
SELECT id, x_coord, y_coord
FROM tmp_nodes
WHERE level_num > 0 OR nodeType = 4                              -- 处理已布局的节点和recall节点
ORDER BY level_num, level_order;                                 -- 按层级和排序遍历

-- 游标结束处理
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

-- 打开游标开始遍历
OPEN node_cursor;

        -- 循环处理每个节点
read_loop: LOOP
            -- 获取下一个节点的信息
            FETCH node_cursor INTO v_node_id, v_x_coord, v_y_coord;
IF done THEN
                LEAVE read_loop;                                              -- 如果没有更多数据，退出循环
END IF;

            -- 检查节点是否存在于JSON中，如果存在则更新坐标
            -- 使用JSON_QUOTE处理包含特殊字符的节点ID
IF JSON_EXTRACT(v_updated_json, CONCAT('$."', v_node_id, '"')) IS NOT NULL THEN
                -- 更新JSON中该节点的X、Y坐标
SET v_updated_json = JSON_SET(
        v_updated_json,
        CONCAT('$."', v_node_id, '".geometry.x'), v_x_coord,        -- 更新X坐标
        CONCAT('$."', v_node_id, '".geometry.y'), v_y_coord         -- 更新Y坐标
                     );
SET v_update_count = v_update_count + 1;                      -- 更新计数器
END IF;

END LOOP;

        -- 关闭游标
CLOSE node_cursor;

        -- 将更新后的JSON数据写回数据库
UPDATE com_paasit_pai_core_processDesignerObj
SET processDesignerData = v_updated_json
WHERE PId = p_process_id;

-- 输出节点坐标更新结果
SELECT CONCAT('成功更新 ', v_update_count, ' 个节点坐标') AS 节点更新结果;
END;

    -- ==================== 14. 更新连线坐标到JSON数据 ====================
    -- 使用游标逐个更新连线坐标到JSON中
BEGIN
    -- 连线更新相关变量声明
    DECLARE done_edge INT DEFAULT FALSE;                                  -- 连线游标结束标志
DECLARE v_edge_id VARCHAR(100);                                       -- 当前处理的连线ID
DECLARE v_start_x INT;                                                 -- 连线起点X坐标
DECLARE v_start_y INT;                                                 -- 连线起点Y坐标
DECLARE v_end_x INT;                                                   -- 连线终点X坐标
DECLARE v_end_y INT;                                                   -- 连线终点Y坐标
DECLARE v_path_points JSON;                                            -- 连线路径点
DECLARE v_line_type VARCHAR(20);                                       -- 连线类型
DECLARE v_edge_update_count INT DEFAULT 0;                            -- 连线更新计数器

        -- 声明连线游标：遍历所有已计算坐标的连线
DECLARE edge_cursor CURSOR FOR
SELECT id, start_x, start_y, end_x, end_y, path_points, line_type
FROM tmp_edges
WHERE is_calculated = TRUE AND is_valid = TRUE
ORDER BY id;

-- 连线游标结束处理
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done_edge = TRUE;

-- 打开连线游标开始遍历
OPEN edge_cursor;

        -- 循环处理每条连线
edge_loop: LOOP
            -- 获取下一条连线的信息
            FETCH edge_cursor INTO v_edge_id, v_start_x, v_start_y, v_end_x, v_end_y, v_path_points, v_line_type;
IF done_edge THEN
                LEAVE edge_loop;                                              -- 如果没有更多数据，退出循环
END IF;

            -- 检查连线是否存在于JSON中，如果存在则更新坐标
            -- 使用JSON_QUOTE处理包含特殊字符的连线ID
IF JSON_EXTRACT(v_updated_json, CONCAT('$."', v_edge_id, '"')) IS NOT NULL THEN
                -- 更新连线的基础坐标信息
SET v_updated_json = JSON_SET(
        v_updated_json,
        CONCAT('$."', v_edge_id, '".geometry.start_x'), v_start_x,   -- 起点X坐标
        CONCAT('$."', v_edge_id, '".geometry.start_y'), v_start_y,   -- 起点Y坐标
        CONCAT('$."', v_edge_id, '".geometry.end_x'), v_end_x,       -- 终点X坐标
        CONCAT('$."', v_edge_id, '".geometry.end_y'), v_end_y,       -- 终点Y坐标
        CONCAT('$."', v_edge_id, '".line_type'), v_line_type         -- 连线类型
                     );

-- 如果有路径点，也更新到JSON中
IF v_path_points IS NOT NULL THEN
SET v_updated_json = JSON_SET(
        v_updated_json,
        CONCAT('$."', v_edge_id, '".geometry.points'), v_path_points  -- 路径点数组
                     );
END IF;

SET v_edge_update_count = v_edge_update_count + 1;            -- 更新计数器
END IF;

END LOOP;

        -- 关闭连线游标
CLOSE edge_cursor;

        -- 输出连线坐标更新结果
SELECT CONCAT('成功更新 ', v_edge_update_count, ' 条连线坐标') AS 连线更新结果;

-- 将最终更新后的JSON数据写回数据库
UPDATE com_paasit_pai_core_processDesignerObj
SET processDesignerData = v_updated_json
WHERE PId = p_process_id;
END;

    -- ==================== 15. 输出处理结果统计 ====================
    -- 显示最终的处理统计信息
    -- 先计算统计数据，避免在SELECT中使用子查询引用临时表
SELECT COUNT(*) INTO @layout_node_count FROM tmp_nodes WHERE level_num > 0;
SELECT COUNT(*) INTO @layout_edge_count FROM tmp_edges WHERE is_calculated = TRUE;

SELECT
    p_process_id AS 流程ID,                                               -- 处理的流程ID
    v_node_count AS 总节点数,                                             -- 总节点数量
    @layout_node_count AS 已布局节点数,                                   -- 成功布局的节点数量
    v_edge_count AS 总连线数,                                             -- 总连线数量
    @layout_edge_count AS 已布局连线数,                                   -- 成功布局的连线数量
    'SUCCESS' AS 处理状态,                                                -- 处理状态
    NOW() AS 完成时间;                                                    -- 完成时间

-- 显示详细的连线布局统计

SELECT * FROM tmp_node_levels;
-- ==================== 16. 提交事务并清理资源 ====================
-- 提交事务，使所有更改生效
COMMIT;

-- 清理临时表，释放内存
DROP TEMPORARY TABLE IF EXISTS tmp_nodes;
DROP TEMPORARY TABLE IF EXISTS tmp_edges;
DROP TEMPORARY TABLE IF EXISTS tmp_node_orders;
DROP TEMPORARY TABLE IF EXISTS tmp_node_levels;
DROP TEMPORARY TABLE IF EXISTS tmp_edge_coordinates;
-- 输出最终完成状态
SELECT '=== 流程模型层级化布局完成（包含连线坐标） ===' AS 最终状态;

END