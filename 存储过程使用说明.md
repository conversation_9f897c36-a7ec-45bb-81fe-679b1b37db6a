# MySQL 8 流程模型层级化布局存储过程

## 概述

本存储过程将 `ProcessModelAnalyzer.java` 中的业务逻辑转换为 MySQL 8 存储过程，实现流程模型的层级化布局算法。主要功能是解析 `processDesignerData` 字段中的JSON数据，计算节点的层级关系，并重新布局节点坐标。

## 文件说明

1. **ProcessModelHierarchicalLayout.sql** - 主要存储过程文件
2. **测试存储过程.sql** - 测试脚本和示例数据
3. **存储过程使用说明.md** - 本说明文档

## 存储过程列表

### 1. ProcessModelHierarchicalLayout
**功能**：完整的层级化布局处理，会更新数据库中的坐标数据

**语法**：
```sql
CALL ProcessModelHierarchicalLayout(p_process_id);
```

**参数**：
- `p_process_id` (VARCHAR(100)): 流程ID，对应表中的 PId 字段

**示例**：
```sql
CALL ProcessModelHierarchicalLayout('c56fea8a-7604-45b8-8e43-01333578c22b');
```

### 2. CalculateProcessLayout
**功能**：仅计算坐标，不更新数据库（只读版本）

**语法**：
```sql
CALL CalculateProcessLayout(p_process_id);
```

**参数**：
- `p_process_id` (VARCHAR(100)): 流程ID

**示例**：
```sql
CALL CalculateProcessLayout('test_process_001');
```

## 核心算法逻辑

### 1. JSON数据解析
- 从 `processDesignerData` 字段解析节点和连线信息
- 识别顶点节点（`vertex = 'true'`）和连线（`edge = 'true'`）
- 提取节点类型、名称、坐标等属性

### 2. 层级计算
- 找到开始节点（`nodeType = 1`）
- 使用递归CTE构建节点层级关系
- 排除recall节点（`nodeType = 4`）
- 实现去重逻辑，每个节点保留最小层级值

### 3. 坐标计算
- **X坐标**：`100 + (层级 - 1) * 300`
- **Y坐标**：
  - 单节点层级：`280`
  - 多节点层级：`200 * 排序序号`

### 4. 数据更新
- 使用游标逐个更新节点坐标
- 通过 `JSON_SET` 函数更新嵌套JSON结构
- 保持原有JSON结构不变，仅更新坐标值

## 节点类型说明

| nodeType | 类型名称 | 说明 |
|----------|----------|------|
| 1 | 开始节点 | 流程起始点 |
| 2 | 初始化节点 | 流程初始化 |
| 4 | 重新提交节点 | 被算法排除 |
| 6 | 审核节点 | 审核处理节点 |
| 8 | 循环节点 | 循环处理节点 |
| 9 | 服务节点 | 服务调用节点 |
| 13 | 网关节点 | 条件分支节点 |
| 99 | 连线 | 节点间连接 |

## 数据库要求

### 1. MySQL版本
- 需要 MySQL 8.0+ 支持JSON函数和递归CTE

### 2. 表结构
```sql
CREATE TABLE com_paasit_pai_core_processDesignerObj (
    PId VARCHAR(100) PRIMARY KEY COMMENT '流程ID',
    processDesignerData JSON COMMENT '流程设计器数据',
    createTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. JSON数据格式
```json
{
  "节点ID": {
    "id": "节点ID",
    "vertex": "true",
    "nodeType": 节点类型数字,
    "nodeName": "节点名称",
    "geometry": {
      "x": X坐标,
      "y": Y坐标,
      "width": 宽度,
      "height": 高度
    }
  },
  "连线ID": {
    "id": "连线ID",
    "edge": "true",
    "source": {"id": "源节点ID"},
    "target": {"id": "目标节点ID"},
    "geometry": {
      "abspoints": [
        {"x": X坐标, "y": Y坐标}
      ]
    }
  }
}
```

## 使用步骤

### 1. 部署存储过程
```sql
-- 执行存储过程创建脚本
SOURCE ProcessModelHierarchicalLayout.sql;
```

### 2. 验证部署
```sql
-- 检查存储过程是否创建成功
SHOW PROCEDURE STATUS WHERE Name LIKE '%ProcessModel%';
```

### 3. 运行测试
```sql
-- 执行测试脚本
SOURCE 测试存储过程.sql;
```

### 4. 实际使用
```sql
-- 对指定流程进行布局处理
CALL ProcessModelHierarchicalLayout('your_process_id');
```

## 错误处理

### 1. 参数验证
- 检查流程ID是否为空
- 验证流程是否存在于数据库中

### 2. 数据验证
- 检查是否找到开始节点
- 验证JSON数据格式正确性

### 3. 事务控制
- 自动回滚机制
- 异常时恢复原始状态

### 4. 常见错误
| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 流程ID不能为空 | 参数为NULL或空字符串 | 提供有效的流程ID |
| 指定的流程ID不存在 | 数据库中无此流程 | 检查流程ID是否正确 |
| 未找到开始节点 | 流程中没有nodeType=1的节点 | 检查流程数据完整性 |

## 性能优化

### 1. 临时表使用
- 使用MEMORY引擎的临时表提高性能
- 自动清理临时表避免内存泄漏

### 2. 索引建议
```sql
-- 在PId字段上创建索引
CREATE INDEX idx_pid ON com_paasit_pai_core_processDesignerObj(PId);
```

### 3. 批量处理
- 单次处理一个流程，避免大批量操作
- 可以通过循环调用处理多个流程

## 监控和日志

### 1. 执行状态输出
存储过程会输出详细的执行状态信息：
- 解析节点数量
- 连线数量统计
- 层级布局结果
- 坐标更新进度

### 2. 结果验证
```sql
-- 查看布局结果
SELECT 
    JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.id')) AS 节点ID,
    CAST(JSON_EXTRACT(node_data, '$.geometry.x') AS SIGNED) AS X坐标,
    CAST(JSON_EXTRACT(node_data, '$.geometry.y') AS SIGNED) AS Y坐标
FROM com_paasit_pai_core_processDesignerObj,
     JSON_TABLE(processDesignerData, '$.*' COLUMNS (node_data JSON PATH '$')) AS jt
WHERE PId = 'your_process_id'
  AND JSON_EXTRACT(node_data, '$.vertex') = 'true'
ORDER BY CAST(JSON_EXTRACT(node_data, '$.geometry.x') AS SIGNED);
```

## 注意事项

1. **备份数据**：执行前建议备份原始数据
2. **测试环境**：先在测试环境验证功能
3. **并发控制**：避免同时处理同一流程
4. **数据一致性**：确保JSON数据格式符合要求
5. **性能监控**：大型流程可能需要较长处理时间

## 技术支持

如有问题，请检查：
1. MySQL版本是否为8.0+
2. JSON数据格式是否正确
3. 表结构是否完整
4. 存储过程是否正确部署
