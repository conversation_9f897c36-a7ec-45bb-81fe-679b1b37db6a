package org.example.test2;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * ProcessModel数据分析器
 * 基于ProcessModelLayouter的解析方法，提供流程模型数据分析功能
 */
public class ProcessModelAnalyzer {
    

    // 默认输入文件路径
    private static final String DEFAULT_INPUT_FILE = "D:\\workspace\\tuspace\\easyFlow\\src\\main\\java\\org\\example\\test3\\processDesignModel.json";
    
    public static void main(String[] args) {
        System.out.println("=== ProcessModel数据分析器启动 ===");
        
        try {
            // 解析命令行参数
            String inputFile = parseInputFile(args);
            System.out.println("输入文件: " + inputFile);
            
            // 执行分析处理
            analyzeProcessModel(inputFile);
            
        } catch (Exception e) {
            System.err.println("处理失败: " + e.getMessage());
            System.exit(1);
        }
        
        System.out.println("=== 分析完成 ===");
    }
    
    /**
     * 解析命令行参数，获取输入文件路径
     */
    private static String parseInputFile(String[] args) {
        if (args.length > 0 && args[0].endsWith(".json")) {
            System.out.println("用户指定输入文件: " + args[0]);
            return args[0];
        }
        
        System.out.println("使用默认输入文件: " + DEFAULT_INPUT_FILE);
        return DEFAULT_INPUT_FILE;
    }
    
    /**
     * 执行ProcessModel分析的主要逻辑
     */
    private static void analyzeProcessModel(String inputPath) throws IOException {
        long startTime = System.currentTimeMillis();
        
        System.out.println("\n开始分析ProcessModel数据结构...");
        
        // 1. 解析JSON文件
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(new File(inputPath));
        
        // 2. 智能查找processModel节点（参考ProcessModelLayouter的实现）
        JsonNode processModel = findProcessModel(root);
        
        if (processModel == null) {
            throw new IllegalArgumentException("JSON文件中未找到processModel节点，请检查文件结构");
        }
        
        System.out.println("✅ 成功找到processModel节点");
        
        // 3. 解析节点信息（参考ProcessModelLayouter的parseNodes方法）
        System.out.println("\n正在解析节点信息...");
        Map<String, NodeData> nodes = parseNodes(processModel);
        System.out.println("解析到 " + nodes.size() + " 个节点");
        
        // 4. 解析边信息（参考ProcessModelLayouter的parseEdges方法）
        System.out.println("\n正在解析连线信息...");
        List<EdgeData> edges = parseEdges(processModel);
        System.out.println("解析到 " + edges.size() + " 条连线");
        
        // 5. 分析结果处理
        System.out.println("\n正在分析数据结构...");
        analyzeResults(nodes, edges);
        
        // 执行层级化布局算法
        System.out.println("\n正在执行层级化布局算法...");
        performHierarchicalLayout(nodes, edges);

        // 更新JSON中的节点和连线坐标
        System.out.println("\n正在更新JSON坐标...");
        updateNodeAndEdgeCoordinates(root, nodes, edges, inputPath);
        
        long endTime = System.currentTimeMillis();
        System.out.println("\n处理时间: " + (endTime - startTime) + "ms");
    }
    
    /**
     * 智能查找processModel节点
     * 参考ProcessModelLayouter的findProcessModel方法实现
     */
    private static JsonNode findProcessModel(JsonNode root) {
        // 尝试直接在根层级查找
        if (root.has("processModel")) {
            System.out.println("找到processModel在根层级");
            return root.get("processModel");
        }

        // 尝试在vueProcessData下查找
        if (root.has("vueProcessData")) {
            JsonNode vueProcessData = root.get("vueProcessData");

            // 直接在vueProcessData下查找
            if (vueProcessData.has("processModel")) {
                System.out.println("找到processModel在vueProcessData层级");
                return vueProcessData.get("processModel");
            }

            // 在vueProcessData.processData下查找
            if (vueProcessData.has("processData")) {
                JsonNode processData = vueProcessData.get("processData");
                if (processData.has("processModel")) {
                    System.out.println("找到processModel在vueProcessData.processData层级");
                    return processData.get("processModel");
                }
            }
        }

        // 递归搜索整个JSON树
        System.out.println("在根层级和vueProcessData中未找到processModel，开始递归搜索...");
        return recursiveFindProcessModel(root);
    }
    
    /**
     * 递归搜索processModel节点
     * 参考ProcessModelLayouter的递归搜索实现
     */
    private static JsonNode recursiveFindProcessModel(JsonNode node) {
        if (node == null) {
            return null;
        }

        // 如果当前节点就是processModel
        if (node.isObject() && node.has("processModel")) {
            return node.get("processModel");
        }

        // 递归搜索子节点
        if (node.isObject()) {
            for (JsonNode child : node) {
                JsonNode result = recursiveFindProcessModel(child);
                if (result != null) {
                    return result;
                }
            }
        } else if (node.isArray()) {
            for (JsonNode child : node) {
                JsonNode result = recursiveFindProcessModel(child);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }
    
    /**
     * 解析节点信息
     * 参考ProcessModelLayouter的parseNodes方法实现
     */
    private static Map<String, NodeData> parseNodes(JsonNode processModel) {
        Map<String, NodeData> nodes = new HashMap<>();
        
        processModel.fields().forEachRemaining(entry -> {
            JsonNode node = entry.getValue();
            if (isVertexNode(node)) {
                NodeData nodeData = new NodeData(entry.getKey(), node);
                nodes.put(entry.getKey(), nodeData);
            }
        });
        
        return nodes;
    }
    
    /**
     * 解析边信息
     * 参考ProcessModelLayouter的parseEdges方法实现
     */
    private static List<EdgeData> parseEdges(JsonNode processModel) {
        List<EdgeData> edges = new ArrayList<>();
        
        processModel.fields().forEachRemaining(entry -> {
            JsonNode node = entry.getValue();
            if (isEdgeNode(node)) {
                EdgeData edgeData = new EdgeData(entry.getKey(), node);
                if (edgeData.isValid()) {
                    edges.add(edgeData);
                }
            }
        });
        
        return edges;
    }
    
    /**
     * 判断是否为顶点节点
     * 参考ProcessModelLayouter的isVertexNode方法
     */
    private static boolean isVertexNode(JsonNode node) {
        return node.has("vertex") && "true".equals(node.get("vertex").asText());
    }
    
    /**
     * 判断是否为边节点
     * 参考ProcessModelLayouter的isEdgeNode方法
     */
    private static boolean isEdgeNode(JsonNode node) {
        return node.has("edge") && "true".equals(node.get("edge").asText());
    }
    
    /**
     * 分析结果处理
     * 参考ProcessModelLayouter的结果处理方式
     */
    private static void analyzeResults(Map<String, NodeData> nodes, List<EdgeData> edges) {
        System.out.println("\n=== 数据结构分析结果 ===");
        
        // 节点类型统计
        Map<Integer, Integer> nodeTypeCount = new HashMap<>();
        for (NodeData node : nodes.values()) {
            int nodeType = node.getNodeType();
            nodeTypeCount.put(nodeType, nodeTypeCount.getOrDefault(nodeType, 0) + 1);
        }
        
        System.out.println("节点类型分布:");
        nodeTypeCount.forEach((type, count) -> {
            String typeName = getNodeTypeName(type);
            System.out.println("  " + typeName + " (类型" + type + "): " + count + " 个");
        });
        
        // 连线统计
        System.out.println("\n连线信息:");
        System.out.println("  总连线数: " + edges.size());
        
        int validEdges = 0;
        for (EdgeData edge : edges) {
            if (edge.hasAbsPoints()) {
                validEdges++;
            }
        }
        System.out.println("  包含路径坐标的连线: " + validEdges + " 条");
        
        // 基础数据结构分析完成
    }

    /**
     * 执行层级化布局算法
     */
    private static void performHierarchicalLayout(Map<String, NodeData> nodes, List<EdgeData> edges) {
        // 1. 找到开始节点（nodeType = 1）
        NodeData startNode = findStartNode(nodes);
        if (startNode == null) {
            System.err.println("警告: 未找到开始节点 (nodeType = 1)");
            return;
        }

        System.out.println("找到开始节点: " + startNode.getId());

        // 2. 构建邻接表（排除recall节点 nodeType = 4）
        Map<String, List<String>> adjacencyList = buildAdjacencyList(edges, nodes);

        // 3. 从开始节点开始层级遍历
        Map<String, Integer> nodeLevels = new HashMap<>();
        traverseHierarchy(startNode.getId(), 1, adjacencyList, nodeLevels, nodes);

        // 4. 设置节点层级
        for (Map.Entry<String, Integer> entry : nodeLevels.entrySet()) {
            String nodeId = entry.getKey();
            int level = entry.getValue();
            if (nodes.containsKey(nodeId)) {
                nodes.get(nodeId).setLevel(level);
            }
        }

        // 5. 按层级分组并排序
        Map<Integer, List<NodeData>> levelGroups = groupNodesByLevel(nodes);

        // 6. 计算坐标
        calculateCoordinates(levelGroups);

        // 7. 输出层级信息
        printHierarchyInfo(levelGroups);
    }

    /**
     * 找到开始节点（nodeType = 1）
     */
    private static NodeData findStartNode(Map<String, NodeData> nodes) {
        for (NodeData node : nodes.values()) {
            if (node.getNodeType() == 1) {
                return node;
            }
        }
        return null;
    }

    /**
     * 构建邻接表，排除recall节点（nodeType = 4）
     */
    private static Map<String, List<String>> buildAdjacencyList(List<EdgeData> edges, Map<String, NodeData> nodes) {
        Map<String, List<String>> adjacencyList = new HashMap<>();

        for (EdgeData edge : edges) {
            if (!edge.isValid()) continue;

            String sourceId = edge.getSourceId();
            String targetId = edge.getTargetId();

            // 排除recall节点（nodeType = 4）作为目标
            if (nodes.containsKey(targetId) && nodes.get(targetId).getNodeType() == 4) {
                continue;
            }

            adjacencyList.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(targetId);
        }

        return adjacencyList;
    }

    /**
     * 层级遍历，实现节点去重逻辑（保留最小level值）
     */
    private static void traverseHierarchy(String nodeId, int level, Map<String, List<String>> adjacencyList,
                                        Map<String, Integer> nodeLevels, Map<String, NodeData> nodes) {
        // 节点去重逻辑：只保留level值最小的记录
        if (nodeLevels.containsKey(nodeId)) {
            if (nodeLevels.get(nodeId) <= level) {
                return; // 已有更小或相等的level，跳过
            }
        }

        nodeLevels.put(nodeId, level);

        // 递归遍历子节点
        List<String> children = adjacencyList.get(nodeId);
        if (children != null) {
            for (String childId : children) {
                if (nodes.containsKey(childId)) {
                    traverseHierarchy(childId, level + 1, adjacencyList, nodeLevels, nodes);
                }
            }
        }
    }

    /**
     * 按层级分组节点并排序
     */
    private static Map<Integer, List<NodeData>> groupNodesByLevel(Map<String, NodeData> nodes) {
        Map<Integer, List<NodeData>> levelGroups = new TreeMap<>();

        for (NodeData node : nodes.values()) {
            if (node.getLevel() > 0) {
                levelGroups.computeIfAbsent(node.getLevel(), k -> new ArrayList<>()).add(node);
            }
        }

        // 在每个层级内按节点ID排序
        for (List<NodeData> levelNodes : levelGroups.values()) {
            levelNodes.sort(Comparator.comparing(NodeData::getId));

            // 设置层级内排序序号
            for (int i = 0; i < levelNodes.size(); i++) {
                levelNodes.get(i).setLevelOrder(i + 1);
            }
        }

        return levelGroups;
    }

    /**
     * 计算节点坐标
     */
    private static void calculateCoordinates(Map<Integer, List<NodeData>> levelGroups) {
        final double START_X = 100;
        final double START_Y = 280;
        final double LEVEL_X_OFFSET = 300;
        final double MULTI_NODE_Y_SPACING = 200;

        for (Map.Entry<Integer, List<NodeData>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<NodeData> levelNodes = entry.getValue();

            // X坐标：每层向右偏移300像素
            double x = START_X + (level - 1) * LEVEL_X_OFFSET;

            if (levelNodes.size() == 1) {
                // 单个节点：保持与开始节点相同的Y坐标
                NodeData node = levelNodes.get(0);
                node.setX(x);
                node.setY(START_Y);
            } else {
                // 多个节点：按排序序号分布
                for (NodeData node : levelNodes) {
                    node.setX(x);
                    node.setY(MULTI_NODE_Y_SPACING * node.getLevelOrder());
                }
            }
        }
    }

    /**
     * 输出层级信息
     */
    private static void printHierarchyInfo(Map<Integer, List<NodeData>> levelGroups) {
        System.out.println("\n=== 层级化布局结果 ===");

        for (Map.Entry<Integer, List<NodeData>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<NodeData> levelNodes = entry.getValue();

            System.out.println("第 " + level + " 层 (" + levelNodes.size() + " 个节点):");
            for (NodeData node : levelNodes) {
                System.out.printf("  节点 %s: 坐标(%.0f, %.0f) - %s%n",
                    node.getId(), node.getX(), node.getY(), node.getNodeName());
            }
        }
    }

    /**
     * 更新JSON中的节点和连线坐标
     */
    private static void updateNodeAndEdgeCoordinates(JsonNode root, Map<String, NodeData> nodes,
                                                   List<EdgeData> edges, String inputPath) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        com.fasterxml.jackson.databind.node.ObjectNode rootCopy = root.deepCopy();

        // 找到processModel节点进行更新
        com.fasterxml.jackson.databind.node.ObjectNode processModelToUpdate = findProcessModelForUpdate(rootCopy);

        if (processModelToUpdate != null) {
            int updatedCount = 0;

            // 更新节点坐标
            for (Map.Entry<String, NodeData> entry : nodes.entrySet()) {
                String nodeId = entry.getKey();
                NodeData nodeData = entry.getValue();

                if (nodeData.getLevel() > 0 && processModelToUpdate.has(nodeId)) {
                    com.fasterxml.jackson.databind.node.ObjectNode nodeObj =
                        (com.fasterxml.jackson.databind.node.ObjectNode) processModelToUpdate.get(nodeId);

                    if (nodeObj.has("geometry")) {
                        com.fasterxml.jackson.databind.node.ObjectNode geometry =
                            (com.fasterxml.jackson.databind.node.ObjectNode) nodeObj.get("geometry");

                        geometry.put("x", (int) nodeData.getX());
                        geometry.put("y", (int) nodeData.getY());
                        updatedCount++;

                        System.out.printf("更新节点 %s 坐标: (%.0f, %.0f)%n",
                            nodeId, nodeData.getX(), nodeData.getY());
                    }
                }
            }

            // 生成输出文件
            String outputPath = inputPath.replace(".json", "_hierarchical_layout.json");
            mapper.writerWithDefaultPrettyPrinter().writeValue(new File(outputPath), rootCopy);

            // 更新连线坐标
            int updatedEdgeCount = updateEdgeCoordinates(processModelToUpdate, nodes, edges);

            System.out.println("\n✅ 坐标更新完成!");
            System.out.println("更新节点数: " + updatedCount);
            System.out.println("更新连线数: " + updatedEdgeCount);
            System.out.println("输出文件: " + outputPath);

        } else {
            System.err.println("警告: 无法找到processModel进行坐标更新");
        }
    }

    /**
     * 查找可更新的processModel节点
     */
    private static com.fasterxml.jackson.databind.node.ObjectNode findProcessModelForUpdate(
            com.fasterxml.jackson.databind.node.ObjectNode root) {

        // 尝试直接在根层级查找
        if (root.has("processModel")) {
            return (com.fasterxml.jackson.databind.node.ObjectNode) root.get("processModel");
        }

        // 尝试在vueProcessData下查找
        if (root.has("vueProcessData")) {
            com.fasterxml.jackson.databind.node.ObjectNode vueProcessData =
                (com.fasterxml.jackson.databind.node.ObjectNode) root.get("vueProcessData");

            if (vueProcessData.has("processModel")) {
                return (com.fasterxml.jackson.databind.node.ObjectNode) vueProcessData.get("processModel");
            }

            if (vueProcessData.has("processData")) {
                com.fasterxml.jackson.databind.node.ObjectNode processData =
                    (com.fasterxml.jackson.databind.node.ObjectNode) vueProcessData.get("processData");
                if (processData.has("processModel")) {
                    return (com.fasterxml.jackson.databind.node.ObjectNode) processData.get("processModel");
                }
            }
        }

        return null;
    }

    /**
     * 更新连线坐标
     */
    private static int updateEdgeCoordinates(com.fasterxml.jackson.databind.node.ObjectNode processModel,
                                           Map<String, NodeData> nodes, List<EdgeData> edges) {
        int updatedCount = 0;

        for (EdgeData edge : edges) {
            if (!edge.isValid() || !processModel.has(edge.getId())) {
                continue;
            }

            String sourceId = edge.getSourceId();
            String targetId = edge.getTargetId();

            // 检查源节点和目标节点是否都有新坐标
            NodeData sourceNode = nodes.get(sourceId);
            NodeData targetNode = nodes.get(targetId);

            if (sourceNode != null && targetNode != null &&
                sourceNode.getLevel() > 0 && targetNode.getLevel() > 0) {

                // 计算新的连线路径
                List<Point> newPath = calculateEdgePath(sourceNode, targetNode);

                // 更新JSON中的abspoints
                com.fasterxml.jackson.databind.node.ObjectNode edgeObj =
                    (com.fasterxml.jackson.databind.node.ObjectNode) processModel.get(edge.getId());

                if (edgeObj.has("geometry")) {
                    com.fasterxml.jackson.databind.node.ObjectNode geometry =
                        (com.fasterxml.jackson.databind.node.ObjectNode) edgeObj.get("geometry");

                    // 创建新的abspoints数组
                    com.fasterxml.jackson.databind.node.ArrayNode abspointsArray =
                        geometry.putArray("abspoints");

                    for (Point point : newPath) {
                        com.fasterxml.jackson.databind.node.ObjectNode pointObj = abspointsArray.addObject();
                        pointObj.put("x", point.x);
                        pointObj.put("y", point.y);
                    }

                    updatedCount++;
                    System.out.printf("更新连线 %s: %s -> %s (%d个路径点)%n",
                        edge.getId(), sourceId, targetId, newPath.size());
                }
            }
        }

        return updatedCount;
    }

    /**
     * 计算连线路径（正交连线，参考原始JSON的连线风格）
     */
    private static List<Point> calculateEdgePath(NodeData sourceNode, NodeData targetNode) {
        List<Point> path = new ArrayList<>();

        // 节点尺寸（从原始JSON中观察得出）
        final int NODE_WIDTH = 180;
        final int NODE_HEIGHT = 110;

        // 计算源节点和目标节点的中心点
        double sourceCenterX = sourceNode.getX() + NODE_WIDTH / 2.0;
        double sourceCenterY = sourceNode.getY() + NODE_HEIGHT / 2.0;

        double targetCenterX = targetNode.getX() + NODE_WIDTH / 2.0;
        double targetCenterY = targetNode.getY() + NODE_HEIGHT / 2.0;

        // 判断连线方向，计算连接点
        double sourceConnectX, sourceConnectY, targetConnectX, targetConnectY;

        if (targetCenterX > sourceCenterX) {
            // 目标在源的右侧：从右边连出，从左边连入
            sourceConnectX = sourceNode.getX() + NODE_WIDTH;
            sourceConnectY = sourceCenterY;
            targetConnectX = targetNode.getX();
            targetConnectY = targetCenterY;
        } else if (targetCenterX < sourceCenterX) {
            // 目标在源的左侧：从左边连出，从右边连入
            sourceConnectX = sourceNode.getX();
            sourceConnectY = sourceCenterY;
            targetConnectX = targetNode.getX() + NODE_WIDTH;
            targetConnectY = targetCenterY;
        } else {
            // 垂直对齐：从下边连出，从上边连入
            sourceConnectX = sourceCenterX;
            sourceConnectY = sourceNode.getY() + NODE_HEIGHT;
            targetConnectX = targetCenterX;
            targetConnectY = targetNode.getY();
        }

        // 起始点
        path.add(new Point((int)sourceConnectX, (int)sourceConnectY));

        // 生成正交路径（类似原始JSON中的Z字形路径）
        if (Math.abs(sourceConnectY - targetConnectY) < 10) {
            // 水平对齐，直接连接
            path.add(new Point((int)targetConnectX, (int)targetConnectY));
        } else {
            // 正交连线：参考原始JSON的4点路径模式
            if (targetCenterX > sourceCenterX) {
                // 向右连接：垂直 -> 水平 -> 垂直
                double midX = sourceConnectX + (targetConnectX - sourceConnectX) / 2.0;

                path.add(new Point((int)sourceConnectX, (int)targetConnectY));  // 垂直到目标Y
                path.add(new Point((int)targetConnectX, (int)targetConnectY)); // 水平到目标X
            } else {
                // 其他情况：使用标准正交路径
                double midY = sourceConnectY + (targetConnectY - sourceConnectY) / 2.0;

                path.add(new Point((int)targetConnectX, (int)sourceConnectY));  // 水平到目标X
                path.add(new Point((int)targetConnectX, (int)targetConnectY)); // 垂直到目标Y
            }
        }

        return path;
    }

    /**
     * 坐标点类
     */
    private static class Point {
        final int x;
        final int y;

        Point(int x, int y) {
            this.x = x;
            this.y = y;
        }
    }

    /**
     * 获取节点类型名称
     */
    private static String getNodeTypeName(int nodeType) {
        switch (nodeType) {
            case 1: return "开始节点";
            case 2: return "初始化节点";
            case 4: return "重新提交节点";
            case 6: return "审核节点";
            case 8: return "循环节点";
            case 9: return "服务节点";
            case 13: return "网关节点";
            case 99: return "连线";
            default: return "未知类型";
        }
    }
    
    /**
     * 节点数据封装类
     */
    private static class NodeData {
        private final String id;
        private final JsonNode jsonNode;
        private final int nodeType;
        private final String nodeName;
        private int level = -1;          // 层级，从1开始
        private int levelOrder = -1;     // 在该层级中的排序序号，从1开始
        private double x = 0;            // X坐标
        private double y = 0;            // Y坐标

        public NodeData(String id, JsonNode jsonNode) {
            this.id = id;
            this.jsonNode = jsonNode;
            this.nodeType = jsonNode.has("nodeType") ? jsonNode.get("nodeType").asInt() : -1;
            this.nodeName = jsonNode.has("nodeName") ? jsonNode.get("nodeName").asText() : "";
        }

        public String getId() { return id; }
        public JsonNode getJsonNode() { return jsonNode; }
        public int getNodeType() { return nodeType; }
        public String getNodeName() { return nodeName; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
        public int getLevelOrder() { return levelOrder; }
        public void setLevelOrder(int levelOrder) { this.levelOrder = levelOrder; }
        public double getX() { return x; }
        public void setX(double x) { this.x = x; }
        public double getY() { return y; }
        public void setY(double y) { this.y = y; }
    }
    
    /**
     * 边数据封装类
     */
    private static class EdgeData {
        private final String id;
        private final JsonNode jsonNode;
        private final boolean valid;
        private final String sourceId;
        private final String targetId;

        public EdgeData(String id, JsonNode jsonNode) {
            this.id = id;
            this.jsonNode = jsonNode;
            this.valid = jsonNode.has("source") && jsonNode.has("target");

            // 提取source和target的ID
            if (valid) {
                JsonNode sourceNode = jsonNode.get("source");
                JsonNode targetNode = jsonNode.get("target");
                this.sourceId = sourceNode.has("id") ? sourceNode.get("id").asText() : "";
                this.targetId = targetNode.has("id") ? targetNode.get("id").asText() : "";
            } else {
                this.sourceId = "";
                this.targetId = "";
            }
        }

        public String getId() { return id; }
        public JsonNode getJsonNode() { return jsonNode; }
        public boolean isValid() { return valid; }
        public String getSourceId() { return sourceId; }
        public String getTargetId() { return targetId; }

        public boolean hasAbsPoints() {
            return jsonNode.has("geometry") &&
                   jsonNode.get("geometry").has("abspoints");
        }
    }
}
