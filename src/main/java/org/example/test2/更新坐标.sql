-- 先找到一共有多少层的节点，从开始节点~结束节点，按照父子关系分层
DROP TABLE IF EXISTS processNodePathList;
DROP TABLE IF EXISTS tmp_final;
CREATE TABLE processNodePathList
SELECT
    JSON_UNQUOTE(JSON_EXTRACT(o, '$.id')) AS id,
    CAST(JSON_EXTRACT(o, '$.nodeType') AS UNSIGNED) AS nodeType,
    JSON_UNQUOTE(JSON_EXTRACT(o, '$.source.id')) AS source_id,
    JSON_UNQUOTE(JSON_EXTRACT(o, '$.target.id')) AS target_id
FROM com_paasit_pai_core_processDesignerObj,
     JSON_TABLE(
             processDesignerData,
             '$.*' COLUMNS (o JSON PATH '$')
     ) AS jt
WHERE PId = 'c56fea8a-7604-45b8-8e43-01333578c22b';

WITH RECURSIVE employee_cte AS (
    -- 基础情况：开始节点
    SELECT  1 as level,id,nodeType, source_id, id as target_id
    FROM processNodePathList
    WHERE nodeType = 1
    UNION ALL
    -- 递归情况：从开始节点往后找
    SELECT ecte.level+1 as leve,e.id,e.nodeType, e.source_id, e.target_id
    FROM processNodePathList e
             INNER JOIN employee_cte ecte ON e.source_id = ecte.target_id
    where e.target_id!='recall' and  NOT FIND_IN_SET(e.target_id, REPLACE(ecte.target_id, '->', ','))
),
-- 去重处理：每个target_id只保留最小level的记录
    deduplicated AS (
        SELECT
            level,
            id,
            nodeType,
            source_id,
            target_id,
            ROW_NUMBER() OVER (
                PARTITION BY target_id
                ORDER BY level
                ) AS rn
        FROM employee_cte
    )

-- 添加层级统计和节点排序
   , with_level_stats AS (
    SELECT
        level,
        id,
        nodeType,
        source_id,
        target_id,
        COUNT(*) OVER (PARTITION BY level) AS level_node_count,
        -- 为每个层级的target_id添加排序
        ROW_NUMBER() OVER (PARTITION BY level ORDER BY target_id) AS level_order
    FROM deduplicated
    WHERE rn = 1
)

-- 计算节点坐标（按新规则）
   , with_coordinates AS (
    SELECT
        level,
        id,
        nodeType,
        source_id,
        target_id,
        level_node_count,
        level_order,
        -- X坐标：每层增加500
        100 + (level - 1) * 300 AS x,
        -- Y坐标：根据层级节点数量计算
        CASE
            WHEN level_node_count = 1 THEN 280  -- 单节点保持280
            ELSE 200 * level_order  -- 多节点：200 * 序号
            END AS y
    FROM with_level_stats
)

-- 最终结果
SELECT
    level,
    id,
    nodeType,
    source_id,
    target_id,
    level_node_count,
    x,
    y,
    level_order
FROM with_coordinates
ORDER BY level, level_order;