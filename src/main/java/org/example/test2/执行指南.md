# MySQL存储过程执行指南

## 问题分析

您遇到的错误 `[Err] 1064 - You have an error in your SQL syntax` 是由于MySQL在解析DELIMITER时遇到问题。这是一个常见的MySQL客户端兼容性问题。

## 解决方案

### 方案1：分离执行（推荐）

**步骤1：** 先执行第一个存储过程
```bash
mysql -u username -p database_name < proc1_UpdateProcessModelLayout.sql
```

**步骤2：** 再执行第二个存储过程
```bash
mysql -u username -p database_name < proc2_BatchUpdateProcessModelLayout.sql
```

### 方案2：使用兼容版本

执行改进的兼容版本：
```bash
mysql -u username -p database_name < processModel_compatible.sql
```

### 方案3：手动执行

如果仍有问题，请按以下步骤手动执行：

1. **连接到MySQL**
```bash
mysql -u username -p database_name
```

2. **执行第一个存储过程**
```sql
DELIMITER $$

DROP PROCEDURE IF EXISTS UpdateProcessModelLayout$$

CREATE PROCEDURE UpdateProcessModelLayout(
    IN p_process_id VARCHAR(255)
)
main_proc: BEGIN
    -- 存储过程内容...
END$$

DELIMITER ;
```

3. **执行第二个存储过程**
```sql
DELIMITER $$

DROP PROCEDURE IF EXISTS BatchUpdateProcessModelLayout$$

CREATE PROCEDURE BatchUpdateProcessModelLayout()
batch_proc: BEGIN
    -- 存储过程内容...
END$$

DELIMITER ;
```

## 客户端兼容性说明

### 推荐的MySQL客户端
- **MySQL命令行客户端** (mysql.exe) - 最佳兼容性
- **MySQL Workbench** - 通常兼容
- **DBeaver** - 需要特殊设置

### 不推荐的客户端
- **Navicat** - DELIMITER支持有限
- **phpMyAdmin** - 可能有解析问题
- **HeidiSQL** - 某些版本有问题

## 故障排除

### 如果仍然出错：

1. **检查MySQL版本**
```sql
SELECT VERSION();
```

2. **使用命令行执行**
```bash
# Windows
mysql.exe -u username -p database_name < filename.sql

# Linux/Mac
mysql -u username -p database_name < filename.sql
```

3. **逐行执行**
将存储过程内容复制到MySQL命令行中逐行执行

4. **检查文件编码**
确保SQL文件使用UTF-8编码，无BOM

## 使用方法

### 处理单个流程
```sql
CALL UpdateProcessModelLayout('your-process-id-here');
```

### 批量处理所有流程
```sql
CALL BatchUpdateProcessModelLayout();
```

### 示例
```sql
CALL UpdateProcessModelLayout('c56fea8a-7604-45b8-8e43-01333578c22b');
```

## 权限要求

确保数据库用户具有以下权限：
- CREATE ROUTINE
- ALTER ROUTINE
- EXECUTE
- SELECT, UPDATE (对相关表)
- CREATE TEMPORARY TABLES

## 注意事项

1. **备份数据**：执行前请备份相关数据表
2. **测试环境**：建议先在测试环境验证
3. **性能考虑**：大型流程图处理可能需要较长时间
4. **事务处理**：存储过程包含完整的事务处理和错误回滚机制

## 联系支持

如果问题仍然存在，请提供：
- MySQL版本信息
- 使用的客户端工具
- 完整的错误信息
- 执行环境（操作系统等）
