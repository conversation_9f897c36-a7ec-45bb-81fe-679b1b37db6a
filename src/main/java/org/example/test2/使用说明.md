# MySQL存储过程使用说明

## 📁 文件说明

- **`processModel_single.sql`** - 简化版MySQL存储过程（推荐使用）
  - 只包含单个流程处理功能
  - 修复了MySQL 8.0兼容性问题
  - 使用标准MySQL语法，避免了FROM子句问题

- **`processModel_final.txt`** - 原始完整版本
  - 包含详细注释和说明
  - 仅作参考使用

## 🚀 快速开始

### 1. 执行存储过程

```sql
-- 在MySQL客户端中执行
source /path/to/processModel_single.sql;

-- 或者复制粘贴整个文件内容到MySQL Workbench中执行
```

### 2. 调用存储过程

```sql
-- 处理指定流程ID的布局
CALL UpdateProcessModelLayout('your-process-id-here');

-- 示例
CALL UpdateProcessModelLayout('c56fea8a-7604-45b8-8e43-01333578c22b');
```

## 🔧 主要修复内容

### 解决的语法问题：
1. **UPDATE FROM语法问题** - 改为使用临时表和游标
2. **DELIMITER兼容性** - 优化了分隔符使用
3. **JSON_TABLE索引问题** - 修正了数组索引计算

### 算法优化：
1. **层级遍历算法** - 从开始节点(nodeType=1)开始
2. **多入口节点处理** - 保留最小level值避免重复
3. **坐标计算** - X轴按层级，Y轴按层内顺序
4. **连线路径更新** - 自动计算连接点坐标

## 📊 输出信息

存储过程执行后会输出以下信息：

1. **处理结果** - 成功状态、处理时间等
2. **节点分布** - 每个节点的层级和坐标
3. **处理摘要** - 总体统计信息
4. **层级统计** - 各层级的节点分布

## ⚠️ 注意事项

### 权限要求：
- `CREATE ROUTINE` - 创建存储过程
- `SELECT, UPDATE` - 操作数据表
- `CREATE TEMPORARY TABLE` - 创建临时表

### 性能考虑：
- 大型流程图处理时间较长
- 建议在低峰期执行
- 包含完整的事务处理机制

### 安全建议：
- 先在测试环境验证
- 备份重要数据
- 监控执行过程

## 🐛 故障排除

### 常见错误：

1. **1064语法错误**
   ```
   解决：使用processModel_single.sql版本
   ```

2. **权限不足**
   ```sql
   GRANT CREATE ROUTINE ON database_name.* TO 'username'@'host';
   GRANT CREATE TEMPORARY TABLES ON database_name.* TO 'username'@'host';
   ```

3. **JSON数据无效**
   ```
   检查processDesignerData字段是否为有效JSON格式
   ```

## 📈 使用流程

```mermaid
graph TD
    A[准备SQL文件] --> B[连接MySQL数据库]
    B --> C[执行processModel_single.sql]
    C --> D[调用存储过程]
    D --> E[查看输出结果]
    E --> F[验证布局更新]
```

## 💡 技术细节

### 核心算法：
1. **BFS层级遍历** - 广度优先搜索确定节点层级
2. **去重优化** - 多入口节点保留最小层级
3. **坐标计算** - 基于层级和顺序的数学公式
4. **路径更新** - 自动计算连线的控制点

### 数据结构：
- 临时表存储节点层级信息
- 游标遍历更新节点坐标
- JSON函数处理流程图数据

## 📞 支持

如果遇到问题，请检查：
1. MySQL版本是否为8.0+
2. 数据库权限是否充足
3. JSON数据格式是否正确
4. 存储过程是否成功创建
