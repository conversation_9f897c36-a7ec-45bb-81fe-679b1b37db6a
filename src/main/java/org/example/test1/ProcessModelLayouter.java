package org.example.test1;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * BPM流程图自动布局处理器
 * 主类，提供完整的流程图布局处理功能
 */
public class ProcessModelLayouter {
    
    private static final Logger logger = LoggerFactory.getLogger(ProcessModelLayouter.class);
    
    public static void main(String[] args) {
        // 验证配置
        if (!LayoutConfig.validateConfig()) {
            System.exit(1);
        }
        
        // 解析命令行参数
        CommandLineArgs cmdArgs = parseCommandLineArgs(args);

        // 执行布局处理
        try {
            processLayout(cmdArgs.algorithm, cmdArgs.inputFile);
        } catch (Exception e) {
            logger.error("布局处理失败", e);
            System.err.println("处理失败: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * 解析命令行参数
     * 支持格式：
     * - 无参数：使用默认算法和默认文件
     * - 1个参数：指定算法
     * - 2个参数：指定算法和输入文件
     */
    private static CommandLineArgs parseCommandLineArgs(String[] args) {
        LayoutConfig.LayoutAlgorithm userAlgorithm = LayoutConfig.DEFAULT_ALGORITHM;
        String inputFile = "atestfile/合同审核__总部职能-财务部_合同审批流程【BPM未美化的json】.json"; // 默认文件

        if (args.length > 0) {
            try {
                userAlgorithm = LayoutConfig.LayoutAlgorithm.valueOf(args[0].toUpperCase());
                System.out.println("用户指定布局算法: " + userAlgorithm);
            } catch (IllegalArgumentException e) {
                // 如果第一个参数不是算法，可能是文件名
                if (args[0].endsWith(".json")) {
                    inputFile = args[0];
                    System.out.println("用户指定输入文件: " + inputFile);
                } else {
                    System.out.println("无效的布局算法参数: " + args[0]);
                    System.out.println("支持的算法: " + Arrays.toString(LayoutConfig.LayoutAlgorithm.values()));
                    System.out.println("使用默认算法: " + userAlgorithm);
                }
            }
        }

        if (args.length > 1) {
            if (args[1].endsWith(".json")) {
                inputFile = args[1];
                System.out.println("用户指定输入文件: " + inputFile);
            }
        }

        return new CommandLineArgs(userAlgorithm, inputFile);
    }

    /**
     * 命令行参数封装类
     */
    private static class CommandLineArgs {
        final LayoutConfig.LayoutAlgorithm algorithm;
        final String inputFile;

        CommandLineArgs(LayoutConfig.LayoutAlgorithm algorithm, String inputFile) {
            this.algorithm = algorithm;
            this.inputFile = inputFile;
        }
    }
    
    /**
     * 执行布局处理的主要逻辑
     */
    private static void processLayout(LayoutConfig.LayoutAlgorithm userAlgorithm, String inputPath) throws IOException {
        long startTime = System.currentTimeMillis();

        // 1. 准备输入输出文件路径
        String fileName = new File(inputPath).getName();
        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        String outputPath = baseName + "_layouted.json";
        
        System.out.println("开始处理BPM流程图布局...");
        System.out.println("输入文件: " + inputPath);
        System.out.println("输出文件: " + outputPath);
        
        // 2. 解析JSON
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(new File(inputPath));

        // 智能查找processModel节点
        JsonNode processModel = findProcessModel(root);

        if (processModel == null) {
            throw new IllegalArgumentException("JSON文件中未找到processModel节点，请检查文件结构");
        }
        
        // 3. 提取节点和边信息
        System.out.println("\n正在解析流程图结构...");
        Map<String, NodeInfo> nodes = parseNodes(processModel);
        List<EdgeInfo> edges = parseEdges(processModel);
        
        System.out.println("解析完成: " + nodes.size() + " 个节点, " + edges.size() + " 条边");
        
        // 4. 智能选择布局算法
        System.out.println("\n正在分析图结构并选择布局算法...");
        LayoutSelector selector = new LayoutSelector();
        LayoutConfig.LayoutAlgorithm algorithm = LayoutConfig.LayoutAlgorithm.HYBRID;
        
        // 验证算法选择
        if (!selector.validateAlgorithmChoice(algorithm, nodes, edges)) {
            System.out.println("算法选择验证失败，但继续执行...");
        }
        
        // 5. 应用选定的布局算法
        System.out.println("正在应用布局算法: " + algorithm);
        applyLayout(nodes, edges, algorithm);

        // 6. 应用改进的边路径优化
        System.out.println("正在优化连接线路径，避免节点穿越和线条重叠...");
        EdgePathOptimizer.optimizeAllEdgePaths(nodes, edges);

        // 7. 生成输出文件
        System.out.println("正在生成输出文件...");
        generateOutputJson(root, nodes, outputPath, processModel);
        
        long endTime = System.currentTimeMillis();
        long processingTime = endTime - startTime;
        
        System.out.println("\n✅ 布局处理完成!");
        System.out.println("使用算法: " + algorithm);
        System.out.println("处理时间: " + processingTime + "ms");
        System.out.println("输出文件: " + outputPath);
        System.out.println("推荐理由: " + selector.getRecommendationReason(algorithm, nodes, edges));
    }
    
    /**
     * 解析节点信息
     */
    private static Map<String, NodeInfo> parseNodes(JsonNode processModel) {
        Map<String, NodeInfo> nodes = new HashMap<>();
        
        processModel.fields().forEachRemaining(entry -> {
            JsonNode node = entry.getValue();
            if (isVertexNode(node)) {
                NodeInfo nodeInfo = new NodeInfo(entry.getKey(), node);
                nodes.put(entry.getKey(), nodeInfo);
            }
        });
        
        return nodes;
    }
    
    /**
     * 解析边信息
     */
    private static List<EdgeInfo> parseEdges(JsonNode processModel) {
        List<EdgeInfo> edges = new ArrayList<>();
        
        processModel.fields().forEachRemaining(entry -> {
            JsonNode node = entry.getValue();
            if (isEdgeNode(node)) {
                EdgeInfo edgeInfo = new EdgeInfo(entry.getKey(), node);
                if (edgeInfo.isValid()) {
                    edges.add(edgeInfo);
                }
            }
        });
        
        return edges;
    }
    
    /**
     * 判断是否为顶点节点
     */
    private static boolean isVertexNode(JsonNode node) {
        return node.has("vertex") && "true".equals(node.get("vertex").asText());
    }
    
    /**
     * 判断是否为边节点
     */
    private static boolean isEdgeNode(JsonNode node) {
        return node.has("edge") && "true".equals(node.get("edge").asText());
    }

    /**
     * 智能查找processModel节点
     * 支持多种JSON结构：
     * 1. 直接在根层级: root.processModel
     * 2. 在vueProcessData下: root.vueProcessData.processModel
     * 3. 在vueProcessData.processData下: root.vueProcessData.processData.processModel
     */
    private static JsonNode findProcessModel(JsonNode root) {
        // 尝试直接在根层级查找
        if (root.has("processModel")) {
            System.out.println("找到processModel在根层级");
            return root.get("processModel");
        }

        // 尝试在vueProcessData下查找
        if (root.has("vueProcessData")) {
            JsonNode vueProcessData = root.get("vueProcessData");

            // 直接在vueProcessData下查找
            if (vueProcessData.has("processModel")) {
                System.out.println("找到processModel在vueProcessData层级");
                return vueProcessData.get("processModel");
            }

            // 在vueProcessData.processData下查找
            if (vueProcessData.has("processData")) {
                JsonNode processData = vueProcessData.get("processData");
                if (processData.has("processModel")) {
                    System.out.println("找到processModel在vueProcessData.processData层级");
                    return processData.get("processModel");
                }
            }
        }

        // 递归搜索整个JSON树
        System.out.println("在根层级和vueProcessData中未找到processModel，开始递归搜索...");
        return recursiveFindProcessModel(root);
    }

    /**
     * 递归搜索processModel节点
     */
    private static JsonNode recursiveFindProcessModel(JsonNode node) {
        if (node == null) {
            return null;
        }

        // 如果当前节点就是processModel
        if (node.isObject() && node.has("processModel")) {
            return node.get("processModel");
        }

        // 递归搜索子节点
        if (node.isObject()) {
            for (JsonNode child : node) {
                JsonNode result = recursiveFindProcessModel(child);
                if (result != null) {
                    return result;
                }
            }
        } else if (node.isArray()) {
            for (JsonNode child : node) {
                JsonNode result = recursiveFindProcessModel(child);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }
    
    /**
     * 应用布局算法
     */
    private static void applyLayout(Map<String, NodeInfo> nodes, List<EdgeInfo> edges,
                                   LayoutConfig.LayoutAlgorithm algorithm) {
        switch (algorithm) {
            case HIERARCHICAL:
                LayoutEngine.applyHierarchicalLayout(nodes, edges);
                break;
            case HIERARCHICAL_OPTIMIZED:
                LayoutEngine.applyOptimizedHierarchicalLayout(nodes, edges);
                break;
            case HIERARCHICAL_EDGE_OPTIMIZED:
                LayoutEngine.applyEdgeOptimizedHierarchicalLayout(nodes, edges);
                break;
            case ORGANIC:
                LayoutEngine.applyOrganicLayout(nodes, edges);
                break;
            case CIRCLE:
                LayoutEngine.applyCircleLayout(nodes, edges);
                break;
            case HYBRID:
                LayoutEngine.applyHybridLayout(nodes, edges);
                break;
            default:
                throw new IllegalArgumentException("不支持的布局算法: " + algorithm);
        }
    }

    /**
     * 生成输出JSON文件
     * 保持完整的JSON结构，只更新processModel中的坐标信息
     */
    private static void generateOutputJson(JsonNode root, Map<String, NodeInfo> nodes,
                                         String outputPath, JsonNode originalProcessModel) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode rootCopy = root.deepCopy();

        // 找到并更新processModel中的坐标
        ObjectNode processModelToUpdate = findAndGetProcessModelForUpdate(rootCopy);

        if (processModelToUpdate != null) {
            // 更新节点坐标
            for (Map.Entry<String, NodeInfo> entry : nodes.entrySet()) {
                String nodeId = entry.getKey();
                NodeInfo nodeInfo = entry.getValue();

                if (processModelToUpdate.has(nodeId)) {
                    ObjectNode nodeObj = (ObjectNode) processModelToUpdate.get(nodeId);
                    if (nodeObj.has("geometry")) {
                        ObjectNode geometry = (ObjectNode) nodeObj.get("geometry");
                        geometry.put("x", (int) nodeInfo.getX());
                        geometry.put("y", (int) nodeInfo.getY());

                        System.out.println("更新节点 " + nodeId + " 坐标: (" +
                                         (int) nodeInfo.getX() + ", " + (int) nodeInfo.getY() + ")");
                    }
                }
            }
        } else {
            System.err.println("警告: 无法找到processModel进行更新");
        }

        // 写入输出文件
        mapper.writerWithDefaultPrettyPrinter()
              .writeValue(new File(outputPath), rootCopy);

        System.out.println("已保存完整的JSON结构到: " + outputPath);
    }

    /**
     * 查找并返回可更新的processModel节点
     */
    private static ObjectNode findAndGetProcessModelForUpdate(ObjectNode root) {
        // 尝试直接在根层级查找
        if (root.has("processModel")) {
            System.out.println("更新根层级的processModel");
            return (ObjectNode) root.get("processModel");
        }

        // 尝试在vueProcessData下查找
        if (root.has("vueProcessData")) {
            ObjectNode vueProcessData = (ObjectNode) root.get("vueProcessData");

            // 直接在vueProcessData下查找
            if (vueProcessData.has("processModel")) {
                System.out.println("更新vueProcessData层级的processModel");
                return (ObjectNode) vueProcessData.get("processModel");
            }

            // 在vueProcessData.processData下查找
            if (vueProcessData.has("processData")) {
                ObjectNode processData = (ObjectNode) vueProcessData.get("processData");
                if (processData.has("processModel")) {
                    System.out.println("更新vueProcessData.processData层级的processModel");
                    return (ObjectNode) processData.get("processModel");
                }
            }
        }

        // 递归搜索
        return recursiveFindProcessModelForUpdate(root);
    }

    /**
     * 递归搜索可更新的processModel节点
     */
    private static ObjectNode recursiveFindProcessModelForUpdate(JsonNode node) {
        if (node == null || !node.isObject()) {
            return null;
        }

        ObjectNode objectNode = (ObjectNode) node;

        // 如果当前节点包含processModel
        if (objectNode.has("processModel")) {
            return (ObjectNode) objectNode.get("processModel");
        }

        // 递归搜索子节点
        for (JsonNode child : objectNode) {
            if (child.isObject()) {
                ObjectNode result = recursiveFindProcessModelForUpdate(child);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }
}
