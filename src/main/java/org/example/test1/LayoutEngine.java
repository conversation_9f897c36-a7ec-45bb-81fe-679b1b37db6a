package org.example.test1;

import java.util.*;

/**
 * 自定义布局引擎
 * 实现层次布局和有机布局算法
 */
public class LayoutEngine {
    
    /**
     * 应用层次布局算法
     * 将节点按层次从上到下排列
     */
    public static void applyHierarchicalLayout(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        System.out.println("应用层次布局算法...");
        
        // 1. 构建图的邻接表
        Map<String, List<String>> graph = buildGraph(nodes, edges);
        Map<String, List<String>> reverseGraph = buildReverseGraph(nodes, edges);
        
        // 2. 找到根节点（入度为0的节点）
        Set<String> rootNodes = findRootNodes(nodes.keySet(), reverseGraph);
        if (rootNodes.isEmpty()) {
            // 如果没有根节点，选择第一个节点作为根
            rootNodes.add(nodes.keySet().iterator().next());
        }
        
        // 3. 使用BFS进行层次分配
        Map<String, Integer> levels = assignLevels(rootNodes, graph);
        
        // 4. 按层排列节点
        arrangeNodesByLevels(nodes, levels);
    }
    
    /**
     * 应用有机布局算法
     * 使用力导向算法排列节点
     */
    public static void applyOrganicLayout(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        System.out.println("应用有机布局算法...");
        
        // 简化的力导向布局
        int iterations = 100;
        double width = 1000;
        double height = 800;
        
        // 初始化随机位置
        Random random = new Random(42); // 固定种子确保结果可重现
        for (NodeInfo node : nodes.values()) {
            node.setX(random.nextDouble() * width);
            node.setY(random.nextDouble() * height);
        }
        
        // 构建邻接表
        Map<String, List<String>> graph = buildGraph(nodes, edges);
        
        // 迭代优化位置
        for (int i = 0; i < iterations; i++) {
            applyForces(nodes, graph, width, height);
        }
    }
    
    /**
     * 应用圆形布局算法
     */
    public static void applyCircleLayout(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        System.out.println("应用圆形布局算法...");

        List<NodeInfo> nodeList = new ArrayList<>(nodes.values());
        int nodeCount = nodeList.size();

        double centerX = 400;
        double centerY = 400;
        double radius = Math.max(200, nodeCount * 20);

        for (int i = 0; i < nodeCount; i++) {
            double angle = 2 * Math.PI * i / nodeCount;
            double x = centerX + radius * Math.cos(angle);
            double y = centerY + radius * Math.sin(angle);

            nodeList.get(i).setX(x);
            nodeList.get(i).setY(y);
        }
    }

    /**
     * 应用混合布局算法
     * 先对局部使用层次布局，再对整体应用有机布局优化
     */
    public static void applyHybridLayout(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        System.out.println("应用混合布局算法...");
        System.out.println("第一阶段：局部层次布局");

        // 第一阶段：检测并分离强连通分量
        List<Set<String>> components = findStronglyConnectedComponents(nodes, edges);
        System.out.println("发现 " + components.size() + " 个强连通分量");

        // 第二阶段：对每个分量应用层次布局
        Map<String, List<String>> graph = buildGraph(nodes, edges);
        Map<String, List<String>> reverseGraph = buildReverseGraph(nodes, edges);

        for (int i = 0; i < components.size(); i++) {
            Set<String> component = components.get(i);
            System.out.println("处理分量 " + (i + 1) + "，包含 " + component.size() + " 个节点");

            if (component.size() == 1) {
                // 单节点分量，保持原位置或简单定位
                continue;
            }

            // 为分量创建子图
            Map<String, NodeInfo> componentNodes = new HashMap<>();
            List<EdgeInfo> componentEdges = new ArrayList<>();

            for (String nodeId : component) {
                componentNodes.put(nodeId, nodes.get(nodeId));
            }

            for (EdgeInfo edge : edges) {
                if (component.contains(edge.getSourceId()) && component.contains(edge.getTargetId())) {
                    componentEdges.add(edge);
                }
            }

            // 对分量应用层次布局
            applyLocalHierarchicalLayout(componentNodes, componentEdges, i);
        }

        System.out.println("第二阶段：整体有机布局优化");

        // 第三阶段：对整体应用有机布局优化
        applyGlobalOrganicOptimization(nodes, edges, 50); // 减少迭代次数，只做微调
    }
    
    /**
     * 构建图的邻接表
     */
    private static Map<String, List<String>> buildGraph(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        Map<String, List<String>> graph = new HashMap<>();
        
        // 初始化所有节点
        for (String nodeId : nodes.keySet()) {
            graph.put(nodeId, new ArrayList<>());
        }
        
        // 添加边
        for (EdgeInfo edge : edges) {
            if (edge.isValid()) {
                String sourceId = edge.getSourceId();
                String targetId = edge.getTargetId();
                
                if (nodes.containsKey(sourceId) && nodes.containsKey(targetId)) {
                    graph.get(sourceId).add(targetId);
                }
            }
        }
        
        return graph;
    }
    
    /**
     * 构建反向图（用于计算入度）
     */
    private static Map<String, List<String>> buildReverseGraph(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        Map<String, List<String>> reverseGraph = new HashMap<>();
        
        // 初始化所有节点
        for (String nodeId : nodes.keySet()) {
            reverseGraph.put(nodeId, new ArrayList<>());
        }
        
        // 添加反向边
        for (EdgeInfo edge : edges) {
            if (edge.isValid()) {
                String sourceId = edge.getSourceId();
                String targetId = edge.getTargetId();
                
                if (nodes.containsKey(sourceId) && nodes.containsKey(targetId)) {
                    reverseGraph.get(targetId).add(sourceId);
                }
            }
        }
        
        return reverseGraph;
    }
    
    /**
     * 找到根节点（入度为0的节点）
     */
    private static Set<String> findRootNodes(Set<String> allNodes, Map<String, List<String>> reverseGraph) {
        Set<String> rootNodes = new HashSet<>();
        
        for (String node : allNodes) {
            if (reverseGraph.get(node).isEmpty()) {
                rootNodes.add(node);
            }
        }
        
        return rootNodes;
    }
    
    /**
     * 使用BFS分配层次
     */
    private static Map<String, Integer> assignLevels(Set<String> rootNodes, Map<String, List<String>> graph) {
        Map<String, Integer> levels = new HashMap<>();
        Queue<String> queue = new LinkedList<>();
        
        // 初始化根节点为第0层
        for (String root : rootNodes) {
            levels.put(root, 0);
            queue.offer(root);
        }
        
        // BFS遍历
        while (!queue.isEmpty()) {
            String current = queue.poll();
            int currentLevel = levels.get(current);
            
            for (String neighbor : graph.get(current)) {
                if (!levels.containsKey(neighbor)) {
                    levels.put(neighbor, currentLevel + 1);
                    queue.offer(neighbor);
                }
            }
        }
        
        // 处理未访问的节点（可能在循环中）
        for (String node : graph.keySet()) {
            if (!levels.containsKey(node)) {
                levels.put(node, 0); // 放在第0层
            }
        }
        
        return levels;
    }
    
    /**
     * 按层次排列节点 - 重新设计，确保绝对不重叠
     */
    private static void arrangeNodesByLevels(Map<String, NodeInfo> nodes, Map<String, Integer> levels) {
        // 按层分组
        Map<Integer, List<NodeInfo>> levelGroups = new HashMap<>();

        for (NodeInfo node : nodes.values()) {
            int level = levels.getOrDefault(node.getId(), 0);
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(node);
        }

        // 计算画布尺寸
        int maxLevel = levelGroups.keySet().stream().mapToInt(Integer::intValue).max().orElse(0);
        int maxNodesInLevel = levelGroups.values().stream().mapToInt(List::size).max().orElse(1);

        // 重新计算画布大小 - 确保足够大
        double totalNodeWidth = maxNodesInLevel * LayoutConfig.NODE_WIDTH;
        double totalSpacing = (maxNodesInLevel - 1) * LayoutConfig.NODE_SPACING_X;
        double canvasWidth = Math.max(2000, totalNodeWidth + totalSpacing + 400); // 增加边距

        double totalNodeHeight = (maxLevel + 1) * LayoutConfig.NODE_HEIGHT;
        double totalVerticalSpacing = maxLevel * LayoutConfig.NODE_SPACING_Y;
        double canvasHeight = Math.max(1500, totalNodeHeight + totalVerticalSpacing + 400); // 增加边距

        System.out.println("重新计算画布尺寸: " + (int)canvasWidth + " x " + (int)canvasHeight);
        System.out.println("最大层级: " + maxLevel + ", 最大层节点数: " + maxNodesInLevel);
        System.out.println("节点尺寸: " + LayoutConfig.NODE_WIDTH + " x " + LayoutConfig.NODE_HEIGHT);
        System.out.println("间距设置: 水平=" + LayoutConfig.NODE_SPACING_X + ", 垂直=" + LayoutConfig.NODE_SPACING_Y);

        // 排列每一层的节点
        double startY = 200;  // 大幅增加顶部边距
        double levelHeight = LayoutConfig.NODE_HEIGHT + LayoutConfig.NODE_SPACING_Y; // 节点高度 + 垂直间距

        for (Map.Entry<Integer, List<NodeInfo>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<NodeInfo> levelNodes = entry.getValue();

            double y = startY + level * levelHeight;

            // 计算该层的总宽度和起始X坐标
            double totalLevelWidth = levelNodes.size() * LayoutConfig.NODE_WIDTH +
                                   (levelNodes.size() - 1) * LayoutConfig.NODE_SPACING_X;
            double startX = Math.max(200, (canvasWidth - totalLevelWidth) / 2); // 居中对齐，大幅增加左边距

            System.out.println("层级 " + level + ": " + levelNodes.size() + " 个节点, Y=" + (int)y +
                             ", 层宽度=" + (int)totalLevelWidth + ", 起始X=" + (int)startX);

            for (int i = 0; i < levelNodes.size(); i++) {
                NodeInfo node = levelNodes.get(i);
                double x = startX + i * (LayoutConfig.NODE_WIDTH + LayoutConfig.NODE_SPACING_X);

                node.setX(x);
                node.setY(y);

                System.out.println("  节点 " + node.getId() + ": (" + (int)x + ", " + (int)y + ")");
            }
        }

        System.out.println("布局完成，总画布尺寸: " + (int)canvasWidth + " x " + (int)canvasHeight);
    }
    
    /**
     * 应用力导向算法的力
     */
    private static void applyForces(Map<String, NodeInfo> nodes, Map<String, List<String>> graph, 
                                   double width, double height) {
        Map<String, Double> forceX = new HashMap<>();
        Map<String, Double> forceY = new HashMap<>();
        
        // 初始化力
        for (String nodeId : nodes.keySet()) {
            forceX.put(nodeId, 0.0);
            forceY.put(nodeId, 0.0);
        }
        
        // 计算排斥力
        for (NodeInfo node1 : nodes.values()) {
            for (NodeInfo node2 : nodes.values()) {
                if (!node1.getId().equals(node2.getId())) {
                    double dx = node1.getX() - node2.getX();
                    double dy = node1.getY() - node2.getY();
                    double distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance > 0) {
                        double force = 1000 / (distance * distance);
                        forceX.put(node1.getId(), forceX.get(node1.getId()) + force * dx / distance);
                        forceY.put(node1.getId(), forceY.get(node1.getId()) + force * dy / distance);
                    }
                }
            }
        }
        
        // 计算吸引力（连接的节点之间）
        for (Map.Entry<String, List<String>> entry : graph.entrySet()) {
            String sourceId = entry.getKey();
            NodeInfo source = nodes.get(sourceId);
            
            for (String targetId : entry.getValue()) {
                NodeInfo target = nodes.get(targetId);
                if (target != null) {
                    double dx = target.getX() - source.getX();
                    double dy = target.getY() - source.getY();
                    double distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance > 0) {
                        double force = distance * 0.01;
                        forceX.put(sourceId, forceX.get(sourceId) + force * dx / distance);
                        forceY.put(sourceId, forceY.get(sourceId) + force * dy / distance);
                        forceX.put(targetId, forceX.get(targetId) - force * dx / distance);
                        forceY.put(targetId, forceY.get(targetId) - force * dy / distance);
                    }
                }
            }
        }
        
        // 应用力并限制在边界内
        for (NodeInfo node : nodes.values()) {
            double fx = forceX.get(node.getId());
            double fy = forceY.get(node.getId());
            
            node.setX(Math.max(50, Math.min(width - 50, node.getX() + fx * 0.1)));
            node.setY(Math.max(50, Math.min(height - 50, node.getY() + fy * 0.1)));
        }
    }

    /**
     * 查找强连通分量（使用Tarjan算法的简化版本）
     */
    private static List<Set<String>> findStronglyConnectedComponents(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        Map<String, List<String>> graph = buildGraph(nodes, edges);
        List<Set<String>> components = new ArrayList<>();
        Set<String> visited = new HashSet<>();

        // 简化版本：使用DFS查找连通分量
        for (String nodeId : nodes.keySet()) {
            if (!visited.contains(nodeId)) {
                Set<String> component = new HashSet<>();
                dfsComponent(nodeId, graph, visited, component);
                components.add(component);
            }
        }

        return components;
    }

    /**
     * DFS查找连通分量
     */
    private static void dfsComponent(String nodeId, Map<String, List<String>> graph,
                                   Set<String> visited, Set<String> component) {
        visited.add(nodeId);
        component.add(nodeId);

        for (String neighbor : graph.getOrDefault(nodeId, new ArrayList<>())) {
            if (!visited.contains(neighbor)) {
                dfsComponent(neighbor, graph, visited, component);
            }
        }
    }

    /**
     * 对局部分量应用层次布局
     */
    private static void applyLocalHierarchicalLayout(Map<String, NodeInfo> componentNodes,
                                                    List<EdgeInfo> componentEdges, int componentIndex) {
        if (componentNodes.isEmpty()) return;

        // 构建分量的图结构
        Map<String, List<String>> graph = buildGraph(componentNodes, componentEdges);
        Map<String, List<String>> reverseGraph = buildReverseGraph(componentNodes, componentEdges);

        // 找到根节点
        Set<String> rootNodes = findRootNodes(componentNodes.keySet(), reverseGraph);
        if (rootNodes.isEmpty()) {
            // 如果没有根节点（循环分量），选择第一个节点
            rootNodes.add(componentNodes.keySet().iterator().next());
        }

        // 分配层次
        Map<String, Integer> levels = assignLevels(rootNodes, graph);

        // 计算分量的布局区域
        double componentWidth = 600;
        double componentHeight = 400;
        double offsetX = (componentIndex % 3) * (componentWidth + 100) + 100;
        double offsetY = (componentIndex / 3) * (componentHeight + 100) + 100;

        // 按层排列节点
        arrangeComponentNodesByLevels(componentNodes, levels, offsetX, offsetY, componentWidth);
    }

    /**
     * 在指定区域内按层次排列分量的节点
     */
    private static void arrangeComponentNodesByLevels(Map<String, NodeInfo> nodes, Map<String, Integer> levels,
                                                     double offsetX, double offsetY, double maxWidth) {
        // 按层分组
        Map<Integer, List<NodeInfo>> levelGroups = new HashMap<>();

        for (NodeInfo node : nodes.values()) {
            int level = levels.getOrDefault(node.getId(), 0);
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(node);
        }

        // 排列每一层的节点
        double levelHeight = 120; // 层间距

        for (Map.Entry<Integer, List<NodeInfo>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<NodeInfo> levelNodes = entry.getValue();

            double y = offsetY + level * levelHeight;
            double nodeSpacing = Math.min(200, maxWidth / Math.max(1, levelNodes.size()));
            double startX = offsetX + (maxWidth - (levelNodes.size() - 1) * nodeSpacing) / 2;

            for (int i = 0; i < levelNodes.size(); i++) {
                NodeInfo node = levelNodes.get(i);
                double x = startX + i * nodeSpacing;

                node.setX(x);
                node.setY(y);
            }
        }
    }

    /**
     * 应用全局有机布局优化
     */
    private static void applyGlobalOrganicOptimization(Map<String, NodeInfo> nodes, List<EdgeInfo> edges, int iterations) {
        System.out.println("开始全局有机优化，迭代次数: " + iterations);

        double width = 2000;  // 增大画布
        double height = 1500;

        // 构建邻接表
        Map<String, List<String>> graph = buildGraph(nodes, edges);

        // 轻量级力导向优化
        for (int i = 0; i < iterations; i++) {
            applyLightweightForces(nodes, graph, width, height);

            if (i % 10 == 0) {
                System.out.println("优化进度: " + (i * 100 / iterations) + "%");
            }
        }

        System.out.println("全局优化完成");
    }

    /**
     * 应用轻量级力（用于微调）
     */
    private static void applyLightweightForces(Map<String, NodeInfo> nodes, Map<String, List<String>> graph,
                                             double width, double height) {
        Map<String, Double> forceX = new HashMap<>();
        Map<String, Double> forceY = new HashMap<>();

        // 初始化力
        for (String nodeId : nodes.keySet()) {
            forceX.put(nodeId, 0.0);
            forceY.put(nodeId, 0.0);
        }

        // 轻量级排斥力（只考虑距离很近的节点）
        for (NodeInfo node1 : nodes.values()) {
            for (NodeInfo node2 : nodes.values()) {
                if (!node1.getId().equals(node2.getId())) {
                    double dx = node1.getX() - node2.getX();
                    double dy = node1.getY() - node2.getY();
                    double distance = Math.sqrt(dx * dx + dy * dy);

                    // 只对距离很近的节点应用排斥力
                    if (distance > 0 && distance < 200) {
                        double force = 500 / (distance * distance);
                        forceX.put(node1.getId(), forceX.get(node1.getId()) + force * dx / distance);
                        forceY.put(node1.getId(), forceY.get(node1.getId()) + force * dy / distance);
                    }
                }
            }
        }

        // 轻量级吸引力（连接的节点之间）
        for (Map.Entry<String, List<String>> entry : graph.entrySet()) {
            String sourceId = entry.getKey();
            NodeInfo source = nodes.get(sourceId);

            for (String targetId : entry.getValue()) {
                NodeInfo target = nodes.get(targetId);
                if (target != null) {
                    double dx = target.getX() - source.getX();
                    double dy = target.getY() - source.getY();
                    double distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance > 0) {
                        double force = distance * 0.005; // 减小吸引力
                        forceX.put(sourceId, forceX.get(sourceId) + force * dx / distance);
                        forceY.put(sourceId, forceY.get(sourceId) + force * dy / distance);
                        forceX.put(targetId, forceX.get(targetId) - force * dx / distance);
                        forceY.put(targetId, forceY.get(targetId) - force * dy / distance);
                    }
                }
            }
        }

        // 应用力（减小步长）
        for (NodeInfo node : nodes.values()) {
            double fx = forceX.get(node.getId());
            double fy = forceY.get(node.getId());

            node.setX(Math.max(50, Math.min(width - 50, node.getX() + fx * 0.05))); // 减小步长
            node.setY(Math.max(50, Math.min(height - 50, node.getY() + fy * 0.05)));
        }
    }

    /**
     * 应用优化的层次布局算法
     * 减少边交叉和重叠问题
     */
    public static void applyOptimizedHierarchicalLayout(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        System.out.println("应用优化层次布局算法...");
        System.out.println("特点：增大间距、减少边交叉、优化节点排序");

        // 1. 构建图的邻接表
        Map<String, List<String>> graph = buildGraph(nodes, edges);
        Map<String, List<String>> reverseGraph = buildReverseGraph(nodes, edges);

        // 2. 找到根节点（入度为0的节点）
        Set<String> rootNodes = findRootNodes(nodes.keySet(), reverseGraph);
        if (rootNodes.isEmpty()) {
            // 如果没有根节点，选择第一个节点作为根
            rootNodes.add(nodes.keySet().iterator().next());
        }

        // 3. 使用BFS进行层次分配
        Map<String, Integer> levels = assignLevels(rootNodes, graph);

        // 4. 优化每层内节点的排序以减少边交叉
        optimizeNodeOrdering(nodes, edges, levels, graph, reverseGraph);

        // 5. 使用优化参数排列节点
        arrangeNodesWithOptimizedSpacing(nodes, levels);
    }

    /**
     * 优化节点排序以减少边交叉
     */
    private static void optimizeNodeOrdering(Map<String, NodeInfo> nodes, List<EdgeInfo> edges,
                                           Map<String, Integer> levels,
                                           Map<String, List<String>> graph,
                                           Map<String, List<String>> reverseGraph) {
        System.out.println("优化节点排序以减少边交叉...");

        // 按层分组
        Map<Integer, List<NodeInfo>> levelGroups = new HashMap<>();
        for (NodeInfo node : nodes.values()) {
            int level = levels.getOrDefault(node.getId(), 0);
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(node);
        }

        // 对每一层应用重心法排序
        for (int level = 1; level <= levelGroups.size() - 1; level++) {
            List<NodeInfo> currentLevel = levelGroups.get(level);
            if (currentLevel == null || currentLevel.size() <= 1) continue;

            // 计算每个节点的重心位置
            List<NodeWithBarycenter> nodeBarycenter = new ArrayList<>();
            for (NodeInfo node : currentLevel) {
                double barycenter = calculateBarycenter(node, levels, levelGroups, reverseGraph);
                nodeBarycenter.add(new NodeWithBarycenter(node, barycenter));
            }

            // 按重心位置排序
            nodeBarycenter.sort(Comparator.comparingDouble(n -> n.barycenter));

            // 更新排序后的节点列表
            List<NodeInfo> sortedNodes = new ArrayList<>();
            for (NodeWithBarycenter nb : nodeBarycenter) {
                sortedNodes.add(nb.node);
            }
            levelGroups.put(level, sortedNodes);

            System.out.println("层级 " + level + " 重新排序: " + sortedNodes.size() + " 个节点");
        }
    }

    /**
     * 计算节点的重心位置（基于上一层连接的节点位置）
     */
    private static double calculateBarycenter(NodeInfo node, Map<String, Integer> levels,
                                            Map<Integer, List<NodeInfo>> levelGroups,
                                            Map<String, List<String>> reverseGraph) {
        List<String> parents = reverseGraph.getOrDefault(node.getId(), new ArrayList<>());
        if (parents.isEmpty()) {
            return 0.0; // 如果没有父节点，返回0
        }

        int nodeLevel = levels.getOrDefault(node.getId(), 0);
        int parentLevel = nodeLevel - 1;
        List<NodeInfo> parentLevelNodes = levelGroups.get(parentLevel);

        if (parentLevelNodes == null) {
            return 0.0;
        }

        double sum = 0.0;
        int count = 0;

        for (int i = 0; i < parentLevelNodes.size(); i++) {
            if (parents.contains(parentLevelNodes.get(i).getId())) {
                sum += i; // 使用父节点在其层级中的索引位置
                count++;
            }
        }

        return count > 0 ? sum / count : 0.0;
    }

    /**
     * 使用优化间距排列节点
     */
    private static void arrangeNodesWithOptimizedSpacing(Map<String, NodeInfo> nodes, Map<String, Integer> levels) {
        // 按层分组
        Map<Integer, List<NodeInfo>> levelGroups = new HashMap<>();

        for (NodeInfo node : nodes.values()) {
            int level = levels.getOrDefault(node.getId(), 0);
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(node);
        }

        // 计算画布尺寸 - 使用优化参数
        int maxLevel = levelGroups.keySet().stream().mapToInt(Integer::intValue).max().orElse(0);
        int maxNodesInLevel = levelGroups.values().stream().mapToInt(List::size).max().orElse(1);

        // 使用优化的间距参数
        double totalNodeWidth = maxNodesInLevel * LayoutConfig.NODE_WIDTH;
        double totalSpacing = (maxNodesInLevel - 1) * LayoutConfig.OPTIMIZED_SPACING_X;
        double canvasWidth = Math.max(3000, totalNodeWidth + totalSpacing + 600); // 更大的边距

        double totalNodeHeight = (maxLevel + 1) * LayoutConfig.NODE_HEIGHT;
        double totalVerticalSpacing = maxLevel * LayoutConfig.OPTIMIZED_SPACING_Y;
        double canvasHeight = Math.max(2000, totalNodeHeight + totalVerticalSpacing + 600); // 更大的边距

        System.out.println("优化布局画布尺寸: " + (int)canvasWidth + " x " + (int)canvasHeight);
        System.out.println("使用优化间距: 水平=" + LayoutConfig.OPTIMIZED_SPACING_X + ", 垂直=" + LayoutConfig.OPTIMIZED_SPACING_Y);

        // 排列每一层的节点
        double startY = 300;  // 更大的顶部边距
        double levelHeight = LayoutConfig.NODE_HEIGHT + LayoutConfig.OPTIMIZED_SPACING_Y;

        for (Map.Entry<Integer, List<NodeInfo>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<NodeInfo> levelNodes = entry.getValue();

            double y = startY + level * levelHeight;

            // 计算该层的总宽度和起始X坐标
            double totalLevelWidth = levelNodes.size() * LayoutConfig.NODE_WIDTH +
                                   (levelNodes.size() - 1) * LayoutConfig.OPTIMIZED_SPACING_X;
            double startX = Math.max(300, (canvasWidth - totalLevelWidth) / 2); // 更大的左边距

            System.out.println("优化层级 " + level + ": " + levelNodes.size() + " 个节点, Y=" + (int)y);

            for (int i = 0; i < levelNodes.size(); i++) {
                NodeInfo node = levelNodes.get(i);
                double x = startX + i * (LayoutConfig.NODE_WIDTH + LayoutConfig.OPTIMIZED_SPACING_X);

                node.setX(x);
                node.setY(y);

                System.out.println("  优化节点 " + node.getId() + ": (" + (int)x + ", " + (int)y + ")");
            }
        }

        System.out.println("优化布局完成，总画布尺寸: " + (int)canvasWidth + " x " + (int)canvasHeight);
    }

    /**
     * 应用边路径优化的层次布局算法
     * 专门解决连接线重叠和交叉问题
     */
    public static void applyEdgeOptimizedHierarchicalLayout(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        System.out.println("应用边路径优化层次布局算法...");
        System.out.println("特点：大幅增加间距、多轮优化排序、边路径优化、避免线穿过节点");

        // 1. 构建图的邻接表
        Map<String, List<String>> graph = buildGraph(nodes, edges);
        Map<String, List<String>> reverseGraph = buildReverseGraph(nodes, edges);

        // 2. 找到根节点（入度为0的节点）
        Set<String> rootNodes = findRootNodes(nodes.keySet(), reverseGraph);
        if (rootNodes.isEmpty()) {
            // 如果没有根节点，选择第一个节点作为根
            rootNodes.add(nodes.keySet().iterator().next());
        }

        // 3. 使用BFS进行层次分配
        Map<String, Integer> levels = assignLevels(rootNodes, graph);

        // 4. 多轮优化节点排序以最小化边交叉
        multiRoundNodeOptimization(nodes, edges, levels, graph, reverseGraph);

        // 5. 使用边优化参数排列节点
        arrangeNodesWithEdgeOptimization(nodes, levels);

        // 6. 优化边路径（可选，为未来扩展预留）
        optimizeEdgePaths(nodes, edges, levels);
    }

    /**
     * 多轮节点排序优化
     */
    private static void multiRoundNodeOptimization(Map<String, NodeInfo> nodes, List<EdgeInfo> edges,
                                                 Map<String, Integer> levels,
                                                 Map<String, List<String>> graph,
                                                 Map<String, List<String>> reverseGraph) {
        System.out.println("开始多轮节点排序优化...");

        // 按层分组
        Map<Integer, List<NodeInfo>> levelGroups = new HashMap<>();
        for (NodeInfo node : nodes.values()) {
            int level = levels.getOrDefault(node.getId(), 0);
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(node);
        }

        // 多轮优化，每轮都尝试减少边交叉
        int maxRounds = 3;
        for (int round = 1; round <= maxRounds; round++) {
            System.out.println("第 " + round + " 轮优化...");

            // 从上到下优化
            for (int level = 1; level <= levelGroups.size() - 1; level++) {
                optimizeLevelOrdering(level, levelGroups, levels, reverseGraph, "下行");
            }

            // 从下到上优化
            for (int level = levelGroups.size() - 2; level >= 1; level--) {
                optimizeLevelOrdering(level, levelGroups, levels, graph, "上行");
            }
        }

        System.out.println("多轮优化完成");
    }

    /**
     * 优化单个层级的节点排序
     */
    private static void optimizeLevelOrdering(int level, Map<Integer, List<NodeInfo>> levelGroups,
                                            Map<String, Integer> levels,
                                            Map<String, List<String>> connectionGraph,
                                            String direction) {
        List<NodeInfo> currentLevel = levelGroups.get(level);
        if (currentLevel == null || currentLevel.size() <= 1) return;

        // 计算每个节点的重心位置
        List<NodeWithBarycenter> nodeBarycenter = new ArrayList<>();
        for (NodeInfo node : currentLevel) {
            double barycenter = calculateAdvancedBarycenter(node, level, levelGroups, levels, connectionGraph, direction);
            nodeBarycenter.add(new NodeWithBarycenter(node, barycenter));
        }

        // 按重心位置排序
        nodeBarycenter.sort(Comparator.comparingDouble(n -> n.barycenter));

        // 更新排序后的节点列表
        List<NodeInfo> sortedNodes = new ArrayList<>();
        for (NodeWithBarycenter nb : nodeBarycenter) {
            sortedNodes.add(nb.node);
        }
        levelGroups.put(level, sortedNodes);

        System.out.println("  " + direction + "优化层级 " + level + ": " + sortedNodes.size() + " 个节点");
    }

    /**
     * 计算高级重心位置
     */
    private static double calculateAdvancedBarycenter(NodeInfo node, int nodeLevel,
                                                    Map<Integer, List<NodeInfo>> levelGroups,
                                                    Map<String, Integer> levels,
                                                    Map<String, List<String>> connectionGraph,
                                                    String direction) {
        List<String> connectedNodes = connectionGraph.getOrDefault(node.getId(), new ArrayList<>());
        if (connectedNodes.isEmpty()) {
            return 0.0;
        }

        int targetLevel = direction.equals("下行") ? nodeLevel - 1 : nodeLevel + 1;
        List<NodeInfo> targetLevelNodes = levelGroups.get(targetLevel);

        if (targetLevelNodes == null) {
            return 0.0;
        }

        double sum = 0.0;
        int count = 0;

        for (int i = 0; i < targetLevelNodes.size(); i++) {
            if (connectedNodes.contains(targetLevelNodes.get(i).getId())) {
                sum += i;
                count++;
            }
        }

        return count > 0 ? sum / count : 0.0;
    }

    /**
     * 使用边优化参数排列节点
     */
    private static void arrangeNodesWithEdgeOptimization(Map<String, NodeInfo> nodes, Map<String, Integer> levels) {
        // 按层分组
        Map<Integer, List<NodeInfo>> levelGroups = new HashMap<>();

        for (NodeInfo node : nodes.values()) {
            int level = levels.getOrDefault(node.getId(), 0);
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(node);
        }

        // 计算画布尺寸 - 使用边优化参数
        int maxLevel = levelGroups.keySet().stream().mapToInt(Integer::intValue).max().orElse(0);
        int maxNodesInLevel = levelGroups.values().stream().mapToInt(List::size).max().orElse(1);

        // 使用边优化的间距参数
        double totalNodeWidth = maxNodesInLevel * LayoutConfig.NODE_WIDTH;
        double totalSpacing = (maxNodesInLevel - 1) * LayoutConfig.EDGE_OPTIMIZED_SPACING_X;
        double canvasWidth = Math.max(4000, totalNodeWidth + totalSpacing + 800); // 更大的边距

        double totalNodeHeight = (maxLevel + 1) * LayoutConfig.NODE_HEIGHT;
        double totalVerticalSpacing = maxLevel * LayoutConfig.EDGE_OPTIMIZED_SPACING_Y;
        double canvasHeight = Math.max(3000, totalNodeHeight + totalVerticalSpacing + 800); // 更大的边距

        System.out.println("边优化布局画布尺寸: " + (int)canvasWidth + " x " + (int)canvasHeight);
        System.out.println("使用边优化间距: 水平=" + LayoutConfig.EDGE_OPTIMIZED_SPACING_X + ", 垂直=" + LayoutConfig.EDGE_OPTIMIZED_SPACING_Y);
        System.out.println("边与节点安全距离: " + LayoutConfig.EDGE_NODE_CLEARANCE + "px");

        // 排列每一层的节点
        double startY = 400;  // 更大的顶部边距
        double levelHeight = LayoutConfig.NODE_HEIGHT + LayoutConfig.EDGE_OPTIMIZED_SPACING_Y;

        for (Map.Entry<Integer, List<NodeInfo>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<NodeInfo> levelNodes = entry.getValue();

            double y = startY + level * levelHeight;

            // 计算该层的总宽度和起始X坐标
            double totalLevelWidth = levelNodes.size() * LayoutConfig.NODE_WIDTH +
                                   (levelNodes.size() - 1) * LayoutConfig.EDGE_OPTIMIZED_SPACING_X;
            double startX = Math.max(400, (canvasWidth - totalLevelWidth) / 2); // 更大的左边距

            System.out.println("边优化层级 " + level + ": " + levelNodes.size() + " 个节点, Y=" + (int)y);

            for (int i = 0; i < levelNodes.size(); i++) {
                NodeInfo node = levelNodes.get(i);
                double x = startX + i * (LayoutConfig.NODE_WIDTH + LayoutConfig.EDGE_OPTIMIZED_SPACING_X);

                node.setX(x);
                node.setY(y);

                System.out.println("  边优化节点 " + node.getId() + ": (" + (int)x + ", " + (int)y + ")");
            }
        }

        System.out.println("边优化布局完成，总画布尺寸: " + (int)canvasWidth + " x " + (int)canvasHeight);
    }

    /**
     * 优化边路径 - 真正的边路径计算
     */
    private static void optimizeEdgePaths(Map<String, NodeInfo> nodes, List<EdgeInfo> edges, Map<String, Integer> levels) {
        System.out.println("开始真正的边路径优化...");
        System.out.println("特点：从节点边缘连接、避免穿过节点、智能路径规划");

        // 使用EdgePathOptimizer进行边路径优化
        EdgePathOptimizer.optimizeAllEdgePaths(nodes, edges);

        System.out.println("边路径优化完成");
    }

    /**
     * 节点和重心位置的辅助类
     */
    private static class NodeWithBarycenter {
        final NodeInfo node;
        final double barycenter;

        NodeWithBarycenter(NodeInfo node, double barycenter) {
            this.node = node;
            this.barycenter = barycenter;
        }
    }
}
