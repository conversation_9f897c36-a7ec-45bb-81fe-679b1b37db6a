package org.example.test1;

import java.util.*;

/**
 * 边路径优化器
 * 负责计算连接线的最优路径，避免穿过节点
 */
public class EdgePathOptimizer {
    
    /**
     * 连接点信息
     */
    public static class ConnectionPoint {
        public final double x;
        public final double y;
        public final String side; // "top", "bottom", "left", "right"
        
        public ConnectionPoint(double x, double y, String side) {
            this.x = x;
            this.y = y;
            this.side = side;
        }
        
        @Override
        public String toString() {
            return String.format("(%.1f, %.1f, %s)", x, y, side);
        }
    }
    
    /**
     * 路径点信息
     */
    public static class RoutePoint {
        public final double x;
        public final double y;

        public RoutePoint(double x, double y) {
            this.x = x;
            this.y = y;
        }

        @Override
        public String toString() {
            return String.format("(%.1f, %.1f)", x, y);
        }
    }
    
    /**
     * 节点矩形信息
     */
    public static class NodeRect {
        public final double x, y, width, height;
        public final String nodeId;
        
        public NodeRect(String nodeId, double x, double y, double width, double height) {
            this.nodeId = nodeId;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
        
        public boolean contains(double px, double py) {
            return px >= x && px <= x + width && py >= y && py <= y + height;
        }
        
        public boolean intersects(double x1, double y1, double x2, double y2) {
            // 检查线段是否与矩形相交
            return lineIntersectsRect(x1, y1, x2, y2, x, y, x + width, y + height);
        }
        
        private boolean lineIntersectsRect(double x1, double y1, double x2, double y2,
                                         double rx1, double ry1, double rx2, double ry2) {
            // 简化的线段与矩形相交检测
            return !(x2 < rx1 || x1 > rx2 || y2 < ry1 || y1 > ry2);
        }
    }
    
    /**
     * 优化所有边的路径
     */
    public static void optimizeAllEdgePaths(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        System.out.println("开始边路径优化...");

        // 1. 构建节点矩形列表
        List<NodeRect> nodeRects = buildNodeRects(nodes);

        // 2. 为每条边计算最优路径
        int optimizedCount = 0;
        List<List<RoutePoint>> allPaths = new ArrayList<>();

        for (EdgeInfo edge : edges) {
            if (optimizeEdgePathWithOverlapAvoidance(edge, nodes, nodeRects, allPaths)) {
                optimizedCount++;
            }
        }

        System.out.println("边路径优化完成: " + optimizedCount + "/" + edges.size() + " 条边被优化");
    }

    /**
     * 优化单条边的路径，避免与已有路径重叠
     */
    private static boolean optimizeEdgePathWithOverlapAvoidance(EdgeInfo edge, Map<String, NodeInfo> nodes,
                                                              List<NodeRect> nodeRects, List<List<RoutePoint>> existingPaths) {
        NodeInfo sourceNode = nodes.get(edge.getSourceId());
        NodeInfo targetNode = nodes.get(edge.getTargetId());

        if (sourceNode == null || targetNode == null) {
            return false;
        }

        // 1. 计算最佳连接点
        ConnectionPoint sourcePoint = calculateBestConnectionPoint(sourceNode, targetNode, "source");
        ConnectionPoint targetPoint = calculateBestConnectionPoint(targetNode, sourceNode, "target");

        // 2. 计算路径，避免与现有路径重叠
        List<RoutePoint> path = calculateOptimalPathWithOverlapAvoidance(sourcePoint, targetPoint, nodeRects,
                                                                        sourceNode.getId(), targetNode.getId(), existingPaths);

        // 3. 更新边的路径信息
        updateEdgePathInfo(edge, sourcePoint, targetPoint, path);

        // 4. 将新路径添加到已有路径列表中
        existingPaths.add(new ArrayList<>(path));

        return true;
    }
    
    /**
     * 构建节点矩形列表
     */
    private static List<NodeRect> buildNodeRects(Map<String, NodeInfo> nodes) {
        List<NodeRect> rects = new ArrayList<>();
        
        for (NodeInfo node : nodes.values()) {
            // 节点矩形包含安全边距
            double margin = LayoutConfig.PATH_SAFETY_MARGIN;
            NodeRect rect = new NodeRect(
                node.getId(),
                node.getX() - margin,
                node.getY() - margin,
                LayoutConfig.NODE_WIDTH + 2 * margin,
                LayoutConfig.NODE_HEIGHT + 2 * margin
            );
            rects.add(rect);
        }
        
        return rects;
    }
    
    /**
     * 优化单条边的路径
     */
    private static boolean optimizeEdgePath(EdgeInfo edge, Map<String, NodeInfo> nodes, List<NodeRect> nodeRects) {
        NodeInfo sourceNode = nodes.get(edge.getSourceId());
        NodeInfo targetNode = nodes.get(edge.getTargetId());
        
        if (sourceNode == null || targetNode == null) {
            return false;
        }
        
        // 1. 计算最佳连接点
        ConnectionPoint sourcePoint = calculateBestConnectionPoint(sourceNode, targetNode, "source");
        ConnectionPoint targetPoint = calculateBestConnectionPoint(targetNode, sourceNode, "target");
        
        // 2. 计算路径
        List<RoutePoint> path = calculateOptimalPath(sourcePoint, targetPoint, nodeRects,
                                                   sourceNode.getId(), targetNode.getId());
        
        // 3. 更新边的路径信息（如果EdgeInfo支持的话）
        updateEdgePathInfo(edge, sourcePoint, targetPoint, path);
        
        return true;
    }
    
    /**
     * 计算最佳连接点 - 优化版本，确保从边缘中心点连接
     */
    private static ConnectionPoint calculateBestConnectionPoint(NodeInfo node, NodeInfo otherNode, String role) {
        double nodeX = node.getX();
        double nodeY = node.getY();
        double nodeWidth = LayoutConfig.NODE_WIDTH;
        double nodeHeight = LayoutConfig.NODE_HEIGHT;

        double otherX = otherNode.getX();
        double otherY = otherNode.getY();

        // 计算节点中心点
        double nodeCenterX = nodeX + nodeWidth / 2;
        double nodeCenterY = nodeY + nodeHeight / 2;
        double otherCenterX = otherX + nodeWidth / 2;
        double otherCenterY = otherY + nodeHeight / 2;

        // 计算方向向量
        double dx = otherCenterX - nodeCenterX;
        double dy = otherCenterY - nodeCenterY;

        // 使用更精确的连接点偏移量
        double edgeOffset = 18.0; // 18像素偏移，在15-20像素范围内

        // 根据主要方向选择最佳连接边
        if (Math.abs(dx) > Math.abs(dy)) {
            // 水平方向为主
            if (dx > 0) {
                // 目标在右侧，从右边缘中心连接
                return new ConnectionPoint(
                    nodeX + nodeWidth + edgeOffset,
                    nodeCenterY, // 右边缘中心点
                    "right"
                );
            } else {
                // 目标在左侧，从左边缘中心连接
                return new ConnectionPoint(
                    nodeX - edgeOffset,
                    nodeCenterY, // 左边缘中心点
                    "left"
                );
            }
        } else {
            // 垂直方向为主
            if (dy > 0) {
                // 目标在下方，从下边缘中心连接
                return new ConnectionPoint(
                    nodeCenterX, // 下边缘中心点
                    nodeY + nodeHeight + edgeOffset,
                    "bottom"
                );
            } else {
                // 目标在上方，从上边缘中心连接
                return new ConnectionPoint(
                    nodeCenterX, // 上边缘中心点
                    nodeY - edgeOffset,
                    "top"
                );
            }
        }
    }
    
    /**
     * 计算最优路径 - 改进版本，支持防重叠和智能避障
     */
    private static List<RoutePoint> calculateOptimalPath(ConnectionPoint source, ConnectionPoint target,
                                                       List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        // 添加起点
        path.add(new RoutePoint(source.x, source.y));

        if (LayoutConfig.USE_ORTHOGONAL_ROUTING) {
            // 使用改进的正交路由
            path.addAll(calculateAdvancedOrthogonalPath(source, target, obstacles, sourceId, targetId));
        } else {
            // 使用改进的直线路由（带避障）
            path.addAll(calculateAdvancedDirectPath(source, target, obstacles, sourceId, targetId));
        }

        // 添加终点
        path.add(new RoutePoint(target.x, target.y));

        // 优化路径，减少不必要的转折点
        return optimizePathPoints(path);
    }

    /**
     * 计算最优路径，避免与现有路径重叠
     */
    private static List<RoutePoint> calculateOptimalPathWithOverlapAvoidance(ConnectionPoint source, ConnectionPoint target,
                                                                           List<NodeRect> obstacles, String sourceId, String targetId,
                                                                           List<List<RoutePoint>> existingPaths) {
        List<RoutePoint> path = new ArrayList<>();

        // 添加起点
        path.add(new RoutePoint(source.x, source.y));

        if (LayoutConfig.USE_ORTHOGONAL_ROUTING) {
            // 使用改进的正交路由，避免路径重叠
            path.addAll(calculateOrthogonalPathWithOverlapAvoidance(source, target, obstacles, sourceId, targetId, existingPaths));
        } else {
            // 使用改进的直线路由（带避障）
            path.addAll(calculateAdvancedDirectPath(source, target, obstacles, sourceId, targetId));
        }

        // 添加终点
        path.add(new RoutePoint(target.x, target.y));

        // 优化路径，减少不必要的转折点
        return optimizePathPoints(path);
    }

    /**
     * 计算正交路径，避免与现有路径重叠
     */
    private static List<RoutePoint> calculateOrthogonalPathWithOverlapAvoidance(ConnectionPoint source, ConnectionPoint target,
                                                                              List<NodeRect> obstacles, String sourceId, String targetId,
                                                                              List<List<RoutePoint>> existingPaths) {
        List<RoutePoint> pathPoints = new ArrayList<>();

        // 计算基本路径策略
        boolean isHorizontalFirst = source.side.equals("right") || source.side.equals("left");

        if (isHorizontalFirst) {
            // 水平优先路径，避免重叠
            pathPoints.addAll(calculateHorizontalFirstPathWithOverlapAvoidance(source, target, obstacles, sourceId, targetId, existingPaths));
        } else {
            // 垂直优先路径，避免重叠
            pathPoints.addAll(calculateVerticalFirstPathWithOverlapAvoidance(source, target, obstacles, sourceId, targetId, existingPaths));
        }

        return pathPoints;
    }
    
    /**
     * 计算改进的正交路径 - 智能避障和防重叠
     */
    private static List<RoutePoint> calculateAdvancedOrthogonalPath(ConnectionPoint source, ConnectionPoint target,
                                                                  List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> pathPoints = new ArrayList<>();

        // 计算基本路径策略
        boolean isHorizontalFirst = source.side.equals("right") || source.side.equals("left");

        if (isHorizontalFirst) {
            // 水平优先路径
            pathPoints.addAll(calculateHorizontalFirstPath(source, target, obstacles, sourceId, targetId));
        } else {
            // 垂直优先路径
            pathPoints.addAll(calculateVerticalFirstPath(source, target, obstacles, sourceId, targetId));
        }

        return pathPoints;
    }

    /**
     * 计算水平优先路径，避免与现有路径重叠
     */
    private static List<RoutePoint> calculateHorizontalFirstPathWithOverlapAvoidance(ConnectionPoint source, ConnectionPoint target,
                                                                                   List<NodeRect> obstacles, String sourceId, String targetId,
                                                                                   List<List<RoutePoint>> existingPaths) {
        List<RoutePoint> path = new ArrayList<>();

        // 计算多个可能的水平延伸距离，避免重叠
        double[] extensions = {80, 120, 160, 200, 250, 300};

        for (double extension : extensions) {
            // 第一步：从源点水平延伸
            double firstX = source.side.equals("right") ?
                           source.x + extension :
                           source.x - extension;

            RoutePoint firstPoint = new RoutePoint(firstX, source.y);

            // 检查第一个点是否安全且不与现有路径重叠
            if (isPointSafeWithMargin(firstPoint.x, firstPoint.y, obstacles, sourceId, targetId) &&
                !isPathOverlappingWithExisting(source.x, source.y, firstPoint.x, firstPoint.y, existingPaths)) {

                path.add(firstPoint);

                // 第二步：垂直移动到目标高度
                RoutePoint secondPoint = new RoutePoint(firstX, target.y);
                if (isPathSafeWithMargin(firstPoint.x, firstPoint.y, secondPoint.x, secondPoint.y, obstacles, sourceId, targetId) &&
                    !isPathOverlappingWithExisting(firstPoint.x, firstPoint.y, secondPoint.x, secondPoint.y, existingPaths)) {

                    path.add(secondPoint);
                    return path; // 找到了合适的路径
                }
            }

            // 清空路径，尝试下一个延伸距离
            path.clear();
        }

        // 如果所有标准路径都有重叠，使用备用策略
        return findAlternativeHorizontalPathWithOverlapAvoidance(source, target, obstacles, sourceId, targetId, existingPaths);
    }

    /**
     * 计算垂直优先路径，避免与现有路径重叠
     */
    private static List<RoutePoint> calculateVerticalFirstPathWithOverlapAvoidance(ConnectionPoint source, ConnectionPoint target,
                                                                                 List<NodeRect> obstacles, String sourceId, String targetId,
                                                                                 List<List<RoutePoint>> existingPaths) {
        List<RoutePoint> path = new ArrayList<>();

        // 计算多个可能的垂直延伸距离，避免重叠
        double[] extensions = {80, 120, 160, 200, 250, 300};

        for (double extension : extensions) {
            // 第一步：从源点垂直延伸
            double firstY = source.side.equals("bottom") ?
                           source.y + extension :
                           source.y - extension;

            RoutePoint firstPoint = new RoutePoint(source.x, firstY);

            // 检查第一个点是否安全且不与现有路径重叠
            if (isPointSafeWithMargin(firstPoint.x, firstPoint.y, obstacles, sourceId, targetId) &&
                !isPathOverlappingWithExisting(source.x, source.y, firstPoint.x, firstPoint.y, existingPaths)) {

                path.add(firstPoint);

                // 第二步：水平移动到目标位置
                RoutePoint secondPoint = new RoutePoint(target.x, firstY);
                if (isPathSafeWithMargin(firstPoint.x, firstPoint.y, secondPoint.x, secondPoint.y, obstacles, sourceId, targetId) &&
                    !isPathOverlappingWithExisting(firstPoint.x, firstPoint.y, secondPoint.x, secondPoint.y, existingPaths)) {

                    path.add(secondPoint);
                    return path; // 找到了合适的路径
                }
            }

            // 清空路径，尝试下一个延伸距离
            path.clear();
        }

        // 如果所有标准路径都有重叠，使用备用策略
        return findAlternativeVerticalPathWithOverlapAvoidance(source, target, obstacles, sourceId, targetId, existingPaths);
    }

    /**
     * 计算水平优先路径
     */
    private static List<RoutePoint> calculateHorizontalFirstPath(ConnectionPoint source, ConnectionPoint target,
                                                               List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        // 计算水平延伸距离
        double horizontalExtension = Math.max(80, Math.abs(target.x - source.x) * 0.3);

        // 第一步：从源点水平延伸
        double firstX = source.side.equals("right") ?
                       source.x + horizontalExtension :
                       source.x - horizontalExtension;

        RoutePoint firstPoint = new RoutePoint(firstX, source.y);

        // 检查第一个点是否安全
        if (isPointSafeWithMargin(firstPoint.x, firstPoint.y, obstacles, sourceId, targetId)) {
            path.add(firstPoint);

            // 第二步：垂直移动到目标高度
            RoutePoint secondPoint = new RoutePoint(firstX, target.y);
            if (isPathSafeWithMargin(firstPoint.x, firstPoint.y, secondPoint.x, secondPoint.y, obstacles, sourceId, targetId)) {
                path.add(secondPoint);
            } else {
                // 寻找安全的垂直路径
                path.addAll(findSafeVerticalPath(firstPoint, target, obstacles, sourceId, targetId));
            }
        } else {
            // 寻找替代的水平路径
            path.addAll(findAlternativeHorizontalPath(source, target, obstacles, sourceId, targetId));
        }

        return path;
    }

    /**
     * 计算垂直优先路径
     */
    private static List<RoutePoint> calculateVerticalFirstPath(ConnectionPoint source, ConnectionPoint target,
                                                             List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        // 计算垂直延伸距离
        double verticalExtension = Math.max(80, Math.abs(target.y - source.y) * 0.3);

        // 第一步：从源点垂直延伸
        double firstY = source.side.equals("bottom") ?
                       source.y + verticalExtension :
                       source.y - verticalExtension;

        RoutePoint firstPoint = new RoutePoint(source.x, firstY);

        // 检查第一个点是否安全
        if (isPointSafeWithMargin(firstPoint.x, firstPoint.y, obstacles, sourceId, targetId)) {
            path.add(firstPoint);

            // 第二步：水平移动到目标位置
            RoutePoint secondPoint = new RoutePoint(target.x, firstY);
            if (isPathSafeWithMargin(firstPoint.x, firstPoint.y, secondPoint.x, secondPoint.y, obstacles, sourceId, targetId)) {
                path.add(secondPoint);
            } else {
                // 寻找安全的水平路径
                path.addAll(findSafeHorizontalPath(firstPoint, target, obstacles, sourceId, targetId));
            }
        } else {
            // 寻找替代的垂直路径
            path.addAll(findAlternativeVerticalPath(source, target, obstacles, sourceId, targetId));
        }

        return path;
    }
    
    /**
     * 计算直线路径（带避障）
     */
    private static List<RoutePoint> calculateDirectPath(ConnectionPoint source, ConnectionPoint target,
                                                      List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        path.add(new RoutePoint(source.x, source.y));

        // 检查直线路径是否安全
        if (isPathSafe(source.x, source.y, target.x, target.y, obstacles, sourceId, targetId)) {
            // 直线路径安全
            path.add(new RoutePoint(target.x, target.y));
        } else {
            // 需要绕行，添加中间点
            RoutePoint detour = findDetourPoint(source, target, obstacles, sourceId, targetId);
            if (detour != null) {
                path.add(detour);
            }
            path.add(new RoutePoint(target.x, target.y));
        }
        
        return path;
    }
    
    /**
     * 寻找安全的正交路径
     */
    private static void findSafeOrthogonalPath(ConnectionPoint source, ConnectionPoint target,
                                              List<NodeRect> obstacles, String sourceId, String targetId,
                                              List<RoutePoint> path) {
        // 简化实现：尝试几个不同的中间点
        double[] offsets = {100, 200, 300, -100, -200, -300};
        
        for (double offset : offsets) {
            double midX, midY;
            
            if (source.side.equals("right") || source.side.equals("left")) {
                midX = source.x + offset;
                midY = source.y;
            } else {
                midX = source.x;
                midY = source.y + offset;
            }
            
            if (isPointSafe(midX, midY, obstacles, sourceId, targetId) &&
                isPathSafe(source.x, source.y, midX, midY, obstacles, sourceId, targetId) &&
                isPathSafe(midX, midY, target.x, target.y, obstacles, sourceId, targetId)) {
                
                path.add(new RoutePoint(midX, midY));
                if (source.side.equals("right") || source.side.equals("left")) {
                    path.add(new RoutePoint(midX, target.y));
                } else {
                    path.add(new RoutePoint(target.x, midY));
                }
                return;
            }
        }

        // 如果找不到安全路径，使用默认中间点
        double midX = (source.x + target.x) / 2;
        double midY = (source.y + target.y) / 2;
        path.add(new RoutePoint(midX, midY));
    }
    
    /**
     * 检查点是否安全（不在任何障碍物内）
     */
    private static boolean isPointSafe(double x, double y, List<NodeRect> obstacles, String sourceId, String targetId) {
        for (NodeRect rect : obstacles) {
            if (!rect.nodeId.equals(sourceId) && !rect.nodeId.equals(targetId)) {
                if (rect.contains(x, y)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查点是否安全（带额外安全边距）
     */
    private static boolean isPointSafeWithMargin(double x, double y, List<NodeRect> obstacles, String sourceId, String targetId) {
        double safetyMargin = LayoutConfig.PATH_SAFETY_MARGIN; // 使用配置的安全边距

        for (NodeRect rect : obstacles) {
            if (!rect.nodeId.equals(sourceId) && !rect.nodeId.equals(targetId)) {
                // 扩展矩形边界
                double expandedX1 = rect.x - safetyMargin;
                double expandedY1 = rect.y - safetyMargin;
                double expandedX2 = rect.x + rect.width + safetyMargin;
                double expandedY2 = rect.y + rect.height + safetyMargin;

                if (x >= expandedX1 && x <= expandedX2 && y >= expandedY1 && y <= expandedY2) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查路径是否安全（带额外安全边距）
     */
    private static boolean isPathSafeWithMargin(double x1, double y1, double x2, double y2,
                                              List<NodeRect> obstacles, String sourceId, String targetId) {
        double safetyMargin = LayoutConfig.PATH_SAFETY_MARGIN; // 使用配置的路径安全边距

        for (NodeRect rect : obstacles) {
            if (!rect.nodeId.equals(sourceId) && !rect.nodeId.equals(targetId)) {
                // 扩展矩形边界
                double expandedX1 = rect.x - safetyMargin;
                double expandedY1 = rect.y - safetyMargin;
                double expandedX2 = rect.x + rect.width + safetyMargin;
                double expandedY2 = rect.y + rect.height + safetyMargin;

                // 检查线段是否与扩展矩形相交
                if (lineIntersectsRect(x1, y1, x2, y2, expandedX1, expandedY1, expandedX2, expandedY2)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查线段是否与矩形相交
     */
    private static boolean lineIntersectsRect(double x1, double y1, double x2, double y2,
                                            double rx1, double ry1, double rx2, double ry2) {
        // 使用更精确的线段与矩形相交算法
        return !(Math.max(x1, x2) < rx1 || Math.min(x1, x2) > rx2 ||
                Math.max(y1, y2) < ry1 || Math.min(y1, y2) > ry2);
    }
    
    /**
     * 检查路径是否安全（不穿过任何障碍物）
     */
    private static boolean isPathSafe(double x1, double y1, double x2, double y2,
                                     List<NodeRect> obstacles, String sourceId, String targetId) {
        for (NodeRect rect : obstacles) {
            if (!rect.nodeId.equals(sourceId) && !rect.nodeId.equals(targetId)) {
                if (rect.intersects(x1, y1, x2, y2)) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * 寻找绕行点
     */
    private static RoutePoint findDetourPoint(ConnectionPoint source, ConnectionPoint target,
                                           List<NodeRect> obstacles, String sourceId, String targetId) {
        // 简化实现：在源和目标之间寻找一个安全的绕行点
        double midX = (source.x + target.x) / 2;
        double midY = (source.y + target.y) / 2;
        
        // 尝试几个不同的偏移
        double[] offsets = {100, -100, 200, -200};
        
        for (double offsetX : offsets) {
            for (double offsetY : offsets) {
                double detourX = midX + offsetX;
                double detourY = midY + offsetY;
                
                if (isPointSafe(detourX, detourY, obstacles, sourceId, targetId) &&
                    isPathSafe(source.x, source.y, detourX, detourY, obstacles, sourceId, targetId) &&
                    isPathSafe(detourX, detourY, target.x, target.y, obstacles, sourceId, targetId)) {
                    
                    return new RoutePoint(detourX, detourY);
                }
            }
        }
        
        return null; // 找不到安全的绕行点
    }

    /**
     * 寻找安全的水平路径
     */
    private static List<RoutePoint> findSafeHorizontalPath(RoutePoint from, ConnectionPoint to,
                                                         List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        // 尝试不同的水平偏移
        double[] offsets = {0, 50, -50, 100, -100, 150, -150};

        for (double offset : offsets) {
            double midX = from.x + offset;
            RoutePoint midPoint = new RoutePoint(midX, from.y);
            RoutePoint endPoint = new RoutePoint(midX, to.y);

            if (isPointSafeWithMargin(midPoint.x, midPoint.y, obstacles, sourceId, targetId) &&
                isPathSafeWithMargin(from.x, from.y, midPoint.x, midPoint.y, obstacles, sourceId, targetId) &&
                isPathSafeWithMargin(midPoint.x, midPoint.y, endPoint.x, endPoint.y, obstacles, sourceId, targetId)) {

                if (offset != 0) path.add(midPoint);
                path.add(endPoint);
                return path;
            }
        }

        // 如果找不到安全路径，返回直接路径
        path.add(new RoutePoint(to.x, from.y));
        return path;
    }

    /**
     * 检查路径是否与现有路径重叠
     */
    private static boolean isPathOverlappingWithExisting(double x1, double y1, double x2, double y2, List<List<RoutePoint>> existingPaths) {
        double overlapTolerance = LayoutConfig.LINE_SEPARATION_DISTANCE; // 线条分离距离

        for (List<RoutePoint> existingPath : existingPaths) {
            for (int i = 0; i < existingPath.size() - 1; i++) {
                RoutePoint p1 = existingPath.get(i);
                RoutePoint p2 = existingPath.get(i + 1);

                // 检查两条线段是否过于接近
                if (areLineSegmentsTooClose(x1, y1, x2, y2, p1.x, p1.y, p2.x, p2.y, overlapTolerance)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检查两条线段是否过于接近
     */
    private static boolean areLineSegmentsTooClose(double x1, double y1, double x2, double y2,
                                                 double x3, double y3, double x4, double y4, double tolerance) {
        // 检查是否为平行线段
        boolean line1IsHorizontal = Math.abs(y2 - y1) < 1.0;
        boolean line1IsVertical = Math.abs(x2 - x1) < 1.0;
        boolean line2IsHorizontal = Math.abs(y4 - y3) < 1.0;
        boolean line2IsVertical = Math.abs(x4 - x3) < 1.0;

        // 如果两条线都是水平的
        if (line1IsHorizontal && line2IsHorizontal) {
            // 检查Y坐标是否过于接近
            if (Math.abs(y1 - y3) < tolerance) {
                // 检查X坐标范围是否重叠
                double minX1 = Math.min(x1, x2);
                double maxX1 = Math.max(x1, x2);
                double minX2 = Math.min(x3, x4);
                double maxX2 = Math.max(x3, x4);

                return !(maxX1 < minX2 || maxX2 < minX1);
            }
        }

        // 如果两条线都是垂直的
        if (line1IsVertical && line2IsVertical) {
            // 检查X坐标是否过于接近
            if (Math.abs(x1 - x3) < tolerance) {
                // 检查Y坐标范围是否重叠
                double minY1 = Math.min(y1, y2);
                double maxY1 = Math.max(y1, y2);
                double minY2 = Math.min(y3, y4);
                double maxY2 = Math.max(y3, y4);

                return !(maxY1 < minY2 || maxY2 < minY1);
            }
        }

        return false;
    }

    /**
     * 寻找替代的水平路径，避免重叠
     */
    private static List<RoutePoint> findAlternativeHorizontalPathWithOverlapAvoidance(ConnectionPoint source, ConnectionPoint target,
                                                                                    List<NodeRect> obstacles, String sourceId, String targetId,
                                                                                    List<List<RoutePoint>> existingPaths) {
        List<RoutePoint> path = new ArrayList<>();

        // 尝试更大的水平延伸和垂直偏移组合
        double[] extensions = {350, 400, 500, -350, -400, -500};
        double[] verticalOffsets = {0, 30, -30, 60, -60, 90, -90};

        for (double ext : extensions) {
            for (double vOffset : verticalOffsets) {
                double firstX = source.side.equals("right") ? source.x + ext : source.x - ext;
                double firstY = source.y + vOffset;
                RoutePoint firstPoint = new RoutePoint(firstX, firstY);

                if (isPointSafeWithMargin(firstPoint.x, firstPoint.y, obstacles, sourceId, targetId) &&
                    !isPathOverlappingWithExisting(source.x, source.y, firstPoint.x, firstPoint.y, existingPaths)) {

                    path.add(firstPoint);

                    // 尝试到达目标
                    RoutePoint secondPoint = new RoutePoint(firstX, target.y);
                    if (isPointSafeWithMargin(secondPoint.x, secondPoint.y, obstacles, sourceId, targetId) &&
                        !isPathOverlappingWithExisting(firstPoint.x, firstPoint.y, secondPoint.x, secondPoint.y, existingPaths)) {

                        path.add(secondPoint);
                        return path;
                    }
                }

                path.clear();
            }
        }

        // 最后的备选方案：使用简单的直角路径
        path.add(new RoutePoint(target.x, source.y));
        return path;
    }

    /**
     * 寻找替代的垂直路径，避免重叠
     */
    private static List<RoutePoint> findAlternativeVerticalPathWithOverlapAvoidance(ConnectionPoint source, ConnectionPoint target,
                                                                                  List<NodeRect> obstacles, String sourceId, String targetId,
                                                                                  List<List<RoutePoint>> existingPaths) {
        List<RoutePoint> path = new ArrayList<>();

        // 尝试更大的垂直延伸和水平偏移组合
        double[] extensions = {350, 400, 500, -350, -400, -500};
        double[] horizontalOffsets = {0, 30, -30, 60, -60, 90, -90};

        for (double ext : extensions) {
            for (double hOffset : horizontalOffsets) {
                double firstY = source.side.equals("bottom") ? source.y + ext : source.y - ext;
                double firstX = source.x + hOffset;
                RoutePoint firstPoint = new RoutePoint(firstX, firstY);

                if (isPointSafeWithMargin(firstPoint.x, firstPoint.y, obstacles, sourceId, targetId) &&
                    !isPathOverlappingWithExisting(source.x, source.y, firstPoint.x, firstPoint.y, existingPaths)) {

                    path.add(firstPoint);

                    // 尝试到达目标
                    RoutePoint secondPoint = new RoutePoint(target.x, firstY);
                    if (isPointSafeWithMargin(secondPoint.x, secondPoint.y, obstacles, sourceId, targetId) &&
                        !isPathOverlappingWithExisting(firstPoint.x, firstPoint.y, secondPoint.x, secondPoint.y, existingPaths)) {

                        path.add(secondPoint);
                        return path;
                    }
                }

                path.clear();
            }
        }

        // 最后的备选方案：使用简单的直角路径
        path.add(new RoutePoint(source.x, target.y));
        return path;
    }

    /**
     * 寻找安全的垂直路径
     */
    private static List<RoutePoint> findSafeVerticalPath(RoutePoint from, ConnectionPoint to,
                                                       List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        // 尝试不同的垂直偏移
        double[] offsets = {0, 50, -50, 100, -100, 150, -150};

        for (double offset : offsets) {
            double midY = from.y + offset;
            RoutePoint midPoint = new RoutePoint(from.x, midY);
            RoutePoint endPoint = new RoutePoint(to.x, midY);

            if (isPointSafeWithMargin(midPoint.x, midPoint.y, obstacles, sourceId, targetId) &&
                isPathSafeWithMargin(from.x, from.y, midPoint.x, midPoint.y, obstacles, sourceId, targetId) &&
                isPathSafeWithMargin(midPoint.x, midPoint.y, endPoint.x, endPoint.y, obstacles, sourceId, targetId)) {

                if (offset != 0) path.add(midPoint);
                path.add(endPoint);
                return path;
            }
        }

        // 如果找不到安全路径，返回直接路径
        path.add(new RoutePoint(from.x, to.y));
        return path;
    }

    /**
     * 寻找替代的水平路径
     */
    private static List<RoutePoint> findAlternativeHorizontalPath(ConnectionPoint source, ConnectionPoint target,
                                                                List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        // 尝试更大的水平延伸
        double[] extensions = {120, 200, 300, -120, -200, -300};

        for (double ext : extensions) {
            double firstX = source.side.equals("right") ? source.x + ext : source.x - ext;
            RoutePoint firstPoint = new RoutePoint(firstX, source.y);

            if (isPointSafeWithMargin(firstPoint.x, firstPoint.y, obstacles, sourceId, targetId)) {
                path.add(firstPoint);
                path.addAll(findSafeVerticalPath(firstPoint, target, obstacles, sourceId, targetId));
                return path;
            }
        }

        // 最后的备选方案
        path.add(new RoutePoint(target.x, source.y));
        return path;
    }

    /**
     * 寻找替代的垂直路径
     */
    private static List<RoutePoint> findAlternativeVerticalPath(ConnectionPoint source, ConnectionPoint target,
                                                              List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        // 尝试更大的垂直延伸
        double[] extensions = {120, 200, 300, -120, -200, -300};

        for (double ext : extensions) {
            double firstY = source.side.equals("bottom") ? source.y + ext : source.y - ext;
            RoutePoint firstPoint = new RoutePoint(source.x, firstY);

            if (isPointSafeWithMargin(firstPoint.x, firstPoint.y, obstacles, sourceId, targetId)) {
                path.add(firstPoint);
                path.addAll(findSafeHorizontalPath(firstPoint, target, obstacles, sourceId, targetId));
                return path;
            }
        }

        // 最后的备选方案
        path.add(new RoutePoint(source.x, target.y));
        return path;
    }

    /**
     * 计算改进的直线路径
     */
    private static List<RoutePoint> calculateAdvancedDirectPath(ConnectionPoint source, ConnectionPoint target,
                                                              List<NodeRect> obstacles, String sourceId, String targetId) {
        List<RoutePoint> path = new ArrayList<>();

        // 检查直线路径是否安全
        if (isPathSafeWithMargin(source.x, source.y, target.x, target.y, obstacles, sourceId, targetId)) {
            // 直线路径安全，不需要中间点
            return path;
        } else {
            // 需要绕行，使用简化的正交路径
            return calculateAdvancedOrthogonalPath(source, target, obstacles, sourceId, targetId);
        }
    }

    /**
     * 优化路径点，减少不必要的转折
     */
    private static List<RoutePoint> optimizePathPoints(List<RoutePoint> originalPath) {
        if (originalPath.size() <= 2) {
            return originalPath;
        }

        List<RoutePoint> optimizedPath = new ArrayList<>();
        optimizedPath.add(originalPath.get(0)); // 添加起点

        // 移除共线的中间点
        for (int i = 1; i < originalPath.size() - 1; i++) {
            RoutePoint prev = originalPath.get(i - 1);
            RoutePoint curr = originalPath.get(i);
            RoutePoint next = originalPath.get(i + 1);

            // 检查三点是否共线
            if (!arePointsCollinear(prev, curr, next)) {
                optimizedPath.add(curr);
            }
        }

        optimizedPath.add(originalPath.get(originalPath.size() - 1)); // 添加终点
        return optimizedPath;
    }

    /**
     * 检查三个点是否共线
     */
    private static boolean arePointsCollinear(RoutePoint p1, RoutePoint p2, RoutePoint p3) {
        double tolerance = 1.0; // 允许的误差

        // 检查是否在同一水平线或垂直线上
        boolean horizontal = Math.abs(p1.y - p2.y) < tolerance && Math.abs(p2.y - p3.y) < tolerance;
        boolean vertical = Math.abs(p1.x - p2.x) < tolerance && Math.abs(p2.x - p3.x) < tolerance;

        return horizontal || vertical;
    }
    
    /**
     * 更新边的路径信息
     */
    private static void updateEdgePathInfo(EdgeInfo edge, ConnectionPoint source, ConnectionPoint target,
                                          List<RoutePoint> path) {
        // 设置连接点
        edge.setSourcePoint(source.x, source.y);
        edge.setTargetPoint(target.x, target.y);

        // 转换路径点
        List<EdgeInfo.PathPoint> edgePathPoints = new ArrayList<>();
        for (RoutePoint p : path) {
            edgePathPoints.add(new EdgeInfo.PathPoint(p.x, p.y));
        }

        // 设置路径
        edge.setPathPoints(edgePathPoints);

        System.out.println("边 " + edge.getSourceId() + " -> " + edge.getTargetId() +
                          " 路径: " + source + " -> " + target + " (经过 " + path.size() + " 个点)");
    }
}
