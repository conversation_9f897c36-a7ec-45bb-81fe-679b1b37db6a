package org.example.test1;

import java.util.*;

/**
 * 循环检测器
 * 使用深度优先搜索(DFS)检测有向图中的循环
 */
public class CycleDetector {
    
    /**
     * 检测图中是否存在循环
     * @param nodes 节点映射
     * @param edges 边列表
     * @return 如果存在循环返回true，否则返回false
     */
    public boolean hasCycle(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        // 构建邻接表
        Map<String, List<String>> graph = buildAdjacencyList(nodes, edges);
        
        Set<String> visited = new HashSet<>();
        Set<String> recStack = new HashSet<>();
        
        // 对每个节点进行DFS检测
        for (String node : graph.keySet()) {
            if (!visited.contains(node)) {
                if (dfsHasCycle(node, visited, recStack, graph)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 查找图中的所有循环路径
     * @param nodes 节点映射
     * @param edges 边列表
     * @return 循环路径列表
     */
    public List<List<String>> findCycles(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        List<List<String>> cycles = new ArrayList<>();
        Map<String, List<String>> graph = buildAdjacencyList(nodes, edges);
        
        Set<String> visited = new HashSet<>();
        Set<String> recStack = new HashSet<>();
        List<String> currentPath = new ArrayList<>();
        
        for (String node : graph.keySet()) {
            if (!visited.contains(node)) {
                dfsFindCycles(node, visited, recStack, currentPath, graph, cycles);
            }
        }
        
        return cycles;
    }
    
    /**
     * 构建邻接表表示的图
     */
    private Map<String, List<String>> buildAdjacencyList(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        Map<String, List<String>> graph = new HashMap<>();
        
        // 初始化所有节点
        for (String nodeId : nodes.keySet()) {
            graph.put(nodeId, new ArrayList<>());
        }
        
        // 添加边
        for (EdgeInfo edge : edges) {
            if (edge.isValid()) {
                String sourceId = edge.getSourceId();
                String targetId = edge.getTargetId();
                
                // 确保源节点和目标节点都存在
                if (nodes.containsKey(sourceId) && nodes.containsKey(targetId)) {
                    graph.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(targetId);
                }
            }
        }
        
        return graph;
    }
    
    /**
     * 使用DFS检测循环
     */
    private boolean dfsHasCycle(String node, Set<String> visited, Set<String> recStack, 
                               Map<String, List<String>> graph) {
        visited.add(node);
        recStack.add(node);
        
        List<String> neighbors = graph.getOrDefault(node, new ArrayList<>());
        for (String neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                if (dfsHasCycle(neighbor, visited, recStack, graph)) {
                    return true;
                }
            } else if (recStack.contains(neighbor)) {
                return true; // 发现循环
            }
        }
        
        recStack.remove(node);
        return false;
    }
    
    /**
     * 使用DFS查找所有循环
     */
    private void dfsFindCycles(String node, Set<String> visited, Set<String> recStack,
                              List<String> currentPath, Map<String, List<String>> graph,
                              List<List<String>> cycles) {
        visited.add(node);
        recStack.add(node);
        currentPath.add(node);
        
        List<String> neighbors = graph.getOrDefault(node, new ArrayList<>());
        for (String neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                dfsFindCycles(neighbor, visited, recStack, currentPath, graph, cycles);
            } else if (recStack.contains(neighbor)) {
                // 发现循环，记录循环路径
                int cycleStart = currentPath.indexOf(neighbor);
                if (cycleStart >= 0) {
                    List<String> cycle = new ArrayList<>(currentPath.subList(cycleStart, currentPath.size()));
                    cycle.add(neighbor); // 闭合循环
                    cycles.add(cycle);
                }
            }
        }
        
        recStack.remove(node);
        currentPath.remove(currentPath.size() - 1);
    }
    
    /**
     * 获取图的统计信息
     */
    public GraphStats getGraphStats(Map<String, NodeInfo> nodes, List<EdgeInfo> edges) {
        Map<String, List<String>> graph = buildAdjacencyList(nodes, edges);
        
        int nodeCount = nodes.size();
        int edgeCount = edges.size();
        boolean hasCycle = hasCycle(nodes, edges);
        
        // 计算连通分量数量
        int connectedComponents = countConnectedComponents(graph);
        
        return new GraphStats(nodeCount, edgeCount, hasCycle, connectedComponents);
    }
    
    /**
     * 计算连通分量数量
     */
    private int countConnectedComponents(Map<String, List<String>> graph) {
        Set<String> visited = new HashSet<>();
        int components = 0;
        
        for (String node : graph.keySet()) {
            if (!visited.contains(node)) {
                dfsVisit(node, visited, graph);
                components++;
            }
        }
        
        return components;
    }
    
    /**
     * DFS访问节点
     */
    private void dfsVisit(String node, Set<String> visited, Map<String, List<String>> graph) {
        visited.add(node);
        List<String> neighbors = graph.getOrDefault(node, new ArrayList<>());
        for (String neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                dfsVisit(neighbor, visited, graph);
            }
        }
    }
    
    /**
     * 图统计信息类
     */
    public static class GraphStats {
        private final int nodeCount;
        private final int edgeCount;
        private final boolean hasCycle;
        private final int connectedComponents;
        
        public GraphStats(int nodeCount, int edgeCount, boolean hasCycle, int connectedComponents) {
            this.nodeCount = nodeCount;
            this.edgeCount = edgeCount;
            this.hasCycle = hasCycle;
            this.connectedComponents = connectedComponents;
        }
        
        // Getters
        public int getNodeCount() { return nodeCount; }
        public int getEdgeCount() { return edgeCount; }
        public boolean hasCycle() { return hasCycle; }
        public int getConnectedComponents() { return connectedComponents; }
        
        @Override
        public String toString() {
            return String.format("GraphStats{nodes=%d, edges=%d, hasCycle=%s, components=%d}", 
                               nodeCount, edgeCount, hasCycle, connectedComponents);
        }
    }
}
