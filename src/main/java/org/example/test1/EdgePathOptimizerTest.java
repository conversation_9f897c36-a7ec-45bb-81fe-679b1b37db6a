package org.example.test1;

import java.util.*;

/**
 * 边路径优化器测试类
 * 用于测试改进的连线避障和防重叠功能
 */
public class EdgePathOptimizerTest {
    
    public static void main(String[] args) {
        System.out.println("=== 边路径优化器测试 ===");
        
        // 创建测试节点
        Map<String, NodeInfo> nodes = createTestNodes();
        
        // 创建测试边
        List<EdgeInfo> edges = createTestEdges();
        
        // 显示测试配置
        System.out.println("测试配置:");
        System.out.println("- 路径安全边距: " + LayoutConfig.PATH_SAFETY_MARGIN + "px");
        System.out.println("- 线条分离距离: " + LayoutConfig.LINE_SEPARATION_DISTANCE + "px");
        System.out.println("- 节点边缘边距: " + LayoutConfig.NODE_EDGE_MARGIN + "px");
        System.out.println("- 使用正交路由: " + LayoutConfig.USE_ORTHOGONAL_ROUTING);
        
        // 执行边路径优化
        System.out.println("\n开始执行边路径优化...");
        EdgePathOptimizer.optimizeAllEdgePaths(nodes, edges);
        
        // 显示优化结果
        System.out.println("\n优化结果:");
        for (EdgeInfo edge : edges) {
            System.out.println("边 " + edge.getId() + " (" + edge.getSourceId() + " -> " + edge.getTargetId() + "):");
            System.out.println("  路径点数量: " + edge.getPathPoints().size());
            if (edge.hasPathInfo()) {
                System.out.println("  路径: " + edge.getPathPoints());
            }
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 创建测试节点
     */
    private static Map<String, NodeInfo> createTestNodes() {
        Map<String, NodeInfo> nodes = new HashMap<>();
        
        // 创建节点1
        NodeInfo node1 = new NodeInfo();
        node1.setId("node1");
        node1.setX(100);
        node1.setY(100);
        node1.setWidth(150);
        node1.setHeight(80);
        nodes.put("node1", node1);
        
        // 创建节点2
        NodeInfo node2 = new NodeInfo();
        node2.setId("node2");
        node2.setX(400);
        node2.setY(100);
        node2.setWidth(150);
        node2.setHeight(80);
        nodes.put("node2", node2);
        
        // 创建节点3
        NodeInfo node3 = new NodeInfo();
        node3.setId("node3");
        node3.setX(700);
        node3.setY(100);
        node3.setWidth(150);
        node3.setHeight(80);
        nodes.put("node3", node3);
        
        // 创建中间障碍节点
        NodeInfo obstacle = new NodeInfo();
        obstacle.setId("obstacle");
        obstacle.setX(400);
        obstacle.setY(250);
        obstacle.setWidth(150);
        obstacle.setHeight(80);
        nodes.put("obstacle", obstacle);
        
        // 创建目标节点
        NodeInfo target = new NodeInfo();
        target.setId("target");
        target.setX(400);
        target.setY(400);
        target.setWidth(150);
        target.setHeight(80);
        nodes.put("target", target);
        
        System.out.println("创建了 " + nodes.size() + " 个测试节点");
        return nodes;
    }
    
    /**
     * 创建测试边
     */
    private static List<EdgeInfo> createTestEdges() {
        List<EdgeInfo> edges = new ArrayList<>();
        
        // 创建边1: node1 -> target (可能穿过obstacle)
        EdgeInfo edge1 = new EdgeInfo();
        edge1.setId("edge1");
        edge1.setSourceId("node1");
        edge1.setTargetId("target");
        edges.add(edge1);
        
        // 创建边2: node2 -> target (可能穿过obstacle)
        EdgeInfo edge2 = new EdgeInfo();
        edge2.setId("edge2");
        edge2.setSourceId("node2");
        edge2.setTargetId("target");
        edges.add(edge2);
        
        // 创建边3: node3 -> target (可能穿过obstacle)
        EdgeInfo edge3 = new EdgeInfo();
        edge3.setId("edge3");
        edge3.setSourceId("node3");
        edge3.setTargetId("target");
        edges.add(edge3);
        
        // 创建边4: node1 -> node3 (水平连接，可能与其他边重叠)
        EdgeInfo edge4 = new EdgeInfo();
        edge4.setId("edge4");
        edge4.setSourceId("node1");
        edge4.setTargetId("node3");
        edges.add(edge4);
        
        System.out.println("创建了 " + edges.size() + " 条测试边");
        return edges;
    }
}
