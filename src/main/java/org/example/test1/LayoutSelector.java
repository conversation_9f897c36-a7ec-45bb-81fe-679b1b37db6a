package org.example.test1;

import java.util.List;
import java.util.Map;

/**
 * 布局选择器
 * 根据图的特征智能选择最适合的布局算法
 */
public class LayoutSelector {
    
    private final CycleDetector cycleDetector;
    
    public LayoutSelector() {
        this.cycleDetector = new CycleDetector();
    }
    
    /**
     * 选择最适合的布局算法
     * @param nodes 节点映射
     * @param edges 边列表
     * @param userChoice 用户指定的算法选择
     * @return 选定的布局算法
     */
    public LayoutConfig.LayoutAlgorithm selectAlgorithm(Map<String, NodeInfo> nodes, 
                                                       List<EdgeInfo> edges, 
                                                       LayoutConfig.LayoutAlgorithm userChoice) {
        
        // 如果用户指定了非AUTO算法，直接使用用户选择
        if (userChoice != LayoutConfig.LayoutAlgorithm.AUTO) {
            System.out.println("使用用户指定的布局算法: " + userChoice);
            System.out.println("算法描述: " + LayoutConfig.getAlgorithmDescription(userChoice));
            return userChoice;
        }
        
        // 获取图的统计信息
        CycleDetector.GraphStats stats = cycleDetector.getGraphStats(nodes, edges);
        System.out.println("图结构分析: " + stats);
        
        // 根据图的特征自动选择算法
        LayoutConfig.LayoutAlgorithm selectedAlgorithm = autoSelectAlgorithm(stats, nodes, edges);
        
        System.out.println("自动选择的布局算法: " + selectedAlgorithm);
        System.out.println("算法描述: " + LayoutConfig.getAlgorithmDescription(selectedAlgorithm));
        
        return selectedAlgorithm;
    }
    
    /**
     * 根据图的特征自动选择算法
     */
    private LayoutConfig.LayoutAlgorithm autoSelectAlgorithm(CycleDetector.GraphStats stats,
                                                           Map<String, NodeInfo> nodes,
                                                           List<EdgeInfo> edges) {
        
        // 检查图的规模
        if (stats.getNodeCount() > LayoutConfig.MAX_NODES) {
            System.out.println("警告: 节点数量(" + stats.getNodeCount() + ")超过推荐最大值(" + LayoutConfig.MAX_NODES + ")");
        }
        
        if (stats.getEdgeCount() > LayoutConfig.MAX_EDGES) {
            System.out.println("警告: 边数量(" + stats.getEdgeCount() + ")超过推荐最大值(" + LayoutConfig.MAX_EDGES + ")");
        }
        
        // 主要决策逻辑：是否存在循环
        if (stats.hasCycle()) {
            System.out.println("检测到循环引用，分析循环复杂度...");
            return selectAlgorithmForCyclicGraph(stats, nodes, edges);
        } else {
            System.out.println("未检测到循环，使用层次布局算法");
            return LayoutConfig.LayoutAlgorithm.HIERARCHICAL;
        }
    }
    
    /**
     * 为有循环的图选择算法
     */
    private LayoutConfig.LayoutAlgorithm selectAlgorithmForCyclicGraph(CycleDetector.GraphStats stats,
                                                                      Map<String, NodeInfo> nodes,
                                                                      List<EdgeInfo> edges) {
        
        // 分析循环的复杂度
        List<List<String>> cycles = cycleDetector.findCycles(nodes, edges);
        
        if (cycles.isEmpty()) {
            // 理论上不应该到这里，因为已经检测到有循环
            System.out.println("循环检测结果不一致，使用有机布局作为安全选择");
            return LayoutConfig.LayoutAlgorithm.ORGANIC;
        }
        
        System.out.println("发现 " + cycles.size() + " 个循环");
        
        // 分析循环特征
        int totalCycleLength = 0;
        int maxCycleLength = 0;
        for (List<String> cycle : cycles) {
            int length = cycle.size() - 1; // 减1因为最后一个节点是重复的
            totalCycleLength += length;
            maxCycleLength = Math.max(maxCycleLength, length);
        }
        
        double avgCycleLength = (double) totalCycleLength / cycles.size();
        
        System.out.println("循环分析: 平均长度=" + String.format("%.1f", avgCycleLength) + 
                          ", 最大长度=" + maxCycleLength + 
                          ", 总数=" + cycles.size());
        
        // 决策逻辑 - 优先使用层次布局，避免节点聚集
        if (cycles.size() == 1 && maxCycleLength <= 6 && stats.getNodeCount() <= 20) {
            // 简单的单循环，节点较少，适合圆形布局
            System.out.println("检测到简单循环结构，使用圆形布局");
            return LayoutConfig.LayoutAlgorithm.CIRCLE;
        } else {
            // 对于复杂流程图，使用边路径优化布局彻底解决重叠问题
            System.out.println("检测到复杂流程结构，使用边路径优化布局避免连接线重叠和交叉");
            System.out.println("特点：大幅增加间距、优化边路径、避免线穿过节点，提供最佳可读性");
            return LayoutConfig.LayoutAlgorithm.HIERARCHICAL_EDGE_OPTIMIZED;
        }
    }
    
    /**
     * 验证算法选择的合理性
     */
    public boolean validateAlgorithmChoice(LayoutConfig.LayoutAlgorithm algorithm,
                                         Map<String, NodeInfo> nodes,
                                         List<EdgeInfo> edges) {
        
        CycleDetector.GraphStats stats = cycleDetector.getGraphStats(nodes, edges);
        
        switch (algorithm) {
            case HIERARCHICAL:
                if (stats.hasCycle()) {
                    System.out.println("警告: 层次布局不适合有循环的图，可能产生不理想的结果");
                    return false;
                }
                break;
            case HIERARCHICAL_OPTIMIZED:
                if (stats.hasCycle()) {
                    System.out.println("警告: 优化层次布局不适合有循环的图，但会尽力减少边交叉");
                    return false; // 仍然允许继续，因为优化版本能更好地处理复杂情况
                }
                break;
            case HIERARCHICAL_EDGE_OPTIMIZED:
                if (stats.hasCycle()) {
                    System.out.println("警告: 边优化布局不适合有循环的图，但会最大化避免边重叠");
                    return false; // 允许继续，专门处理边重叠问题
                }
                break;

            case ORGANIC:
                if (stats.getNodeCount() > 500) {
                    System.out.println("警告: 有机布局在大型图上可能性能较差");
                }
                break;

            case CIRCLE:
                if (stats.getNodeCount() > 50) {
                    System.out.println("警告: 圆形布局不适合大型图");
                    return false;
                }
                break;

            case HYBRID:
                if (stats.getNodeCount() > 1000) {
                    System.out.println("警告: 混合布局在超大型图上可能性能较差");
                }
                break;
        }
        
        return true;
    }
    
    /**
     * 获取算法推荐理由
     */
    public String getRecommendationReason(LayoutConfig.LayoutAlgorithm algorithm,
                                        Map<String, NodeInfo> nodes,
                                        List<EdgeInfo> edges) {
        
        CycleDetector.GraphStats stats = cycleDetector.getGraphStats(nodes, edges);
        
        switch (algorithm) {
            case HIERARCHICAL:
                return "图结构为有向无环图(DAG)，层次布局能提供清晰的从上到下的层次结构";

            case HIERARCHICAL_OPTIMIZED:
                return "复杂流程图，优化层次布局能减少边交叉和重叠，提供更清晰的视觉效果";

            case HIERARCHICAL_EDGE_OPTIMIZED:
                return "复杂流程图，边路径优化布局专门解决连接线重叠和交叉问题，提供最佳可读性";

            case ORGANIC:
                return "图包含循环引用，有机布局能自然地处理复杂的节点关系";

            case CIRCLE:
                return "图结构简单且包含循环，圆形布局能清晰地展示循环关系";

            case HYBRID:
                return "复杂循环图结构，混合布局先保持局部层次结构，再进行整体优化";

            case AUTO:
                return "根据图的特征自动选择最适合的算法";

            default:
                return "未知算法";
        }
    }
}
