package org.example.test1;

/**
 * 布局配置类
 * 定义各种布局算法的配置参数
 */
public class LayoutConfig {
    
    // 基本布局参数 - 大幅增加间距避免节点重叠
    public static final double NODE_SPACING_X = 400.0;  // 水平间距
    public static final double NODE_SPACING_Y = 250.0;  // 垂直间距
    public static final double NODE_WIDTH = 200.0;      // 节点宽度
    public static final double NODE_HEIGHT = 120.0;     // 节点高度

    // 优化布局参数 - 进一步增加间距减少边交叉
    public static final double OPTIMIZED_SPACING_X = 500.0;  // 优化水平间距
    public static final double OPTIMIZED_SPACING_Y = 350.0;  // 优化垂直间距
    public static final double EDGE_CLEARANCE = 50.0;       // 边与节点的最小间隙

    // 边路径优化参数 - 专门解决边重叠和交叉问题
    public static final double EDGE_OPTIMIZED_SPACING_X = 600.0;  // 边优化水平间距
    public static final double EDGE_OPTIMIZED_SPACING_Y = 500.0;  // 边优化垂直间距
    public static final double EDGE_NODE_CLEARANCE = 80.0;       // 边与节点的安全距离
    public static final double EDGE_OFFSET = 30.0;              // 边起点终点偏移

    // 边路径计算参数 - 优化版本
    public static final double NODE_EDGE_MARGIN = 18.0;         // 节点边缘到连接点的距离（15-20像素范围）
    public static final double PATH_SAFETY_MARGIN = 80.0;       // 路径与节点的安全间距（增加到80px）
    public static final boolean USE_ORTHOGONAL_ROUTING = true;   // 使用正交路由
    public static final double ROUTE_GRID_SIZE = 20.0;          // 路由网格大小
    public static final double LINE_SEPARATION_DISTANCE = 25.0; // 多条线之间的分离距离
    public static final double MIN_PATH_SEGMENT_LENGTH = 40.0;   // 最小路径段长度

    public static final boolean DISABLE_EDGE_STYLE = false;
    
    // 有机布局参数
    public static final double FORCE_CONSTANT = 120.0;
    public static final double MIN_DISTANCE_LIMIT = 30.0;
    public static final int MAX_ITERATIONS = 1000;
    
    // 圆形布局参数
    public static final double CIRCLE_RADIUS = 200.0;
    
    // 性能配置
    public static final int MAX_NODES = 1000;
    public static final int MAX_EDGES = 2000;
    public static final long MAX_PROCESSING_TIME = 30000; // 30秒
    
    /**
     * 布局算法枚举
     */
    public enum LayoutAlgorithm {
        AUTO,                       // 自动选择
        HIERARCHICAL,               // 层次布局
        HIERARCHICAL_OPTIMIZED,     // 优化层次布局，减少边交叉
        HIERARCHICAL_EDGE_OPTIMIZED, // 边路径优化布局，避免重叠和交叉
        ORGANIC,                    // 有机布局
        CIRCLE,                     // 圆形布局
        HYBRID                      // 混合布局：局部层次 + 整体有机优化
    }
    
    public static final LayoutAlgorithm DEFAULT_ALGORITHM = LayoutAlgorithm.AUTO;
    
    /**
     * 根据算法类型获取描述
     */
    public static String getAlgorithmDescription(LayoutAlgorithm algorithm) {
        switch (algorithm) {
            case AUTO:
                return "自动选择最适合的布局算法";
            case HIERARCHICAL:
                return "层次布局，适用于无循环的有向无环图";
            case HIERARCHICAL_OPTIMIZED:
                return "优化层次布局，减少边交叉和重叠，适用于复杂流程图";
            case HIERARCHICAL_EDGE_OPTIMIZED:
                return "边路径优化布局，避免连接线与节点重叠，减少线与线交叉，提高可读性";
            case ORGANIC:
                return "有机布局，适用于有循环的复杂图结构";
            case CIRCLE:
                return "圆形布局，适用于简单的循环图";
            case HYBRID:
                return "混合布局，先局部层次布局再整体有机优化";
            default:
                return "未知算法";
        }
    }
    
    /**
     * 验证配置参数的有效性
     */
    public static boolean validateConfig() {
        if (NODE_SPACING_X <= 0 || NODE_SPACING_Y <= 0) {
            System.err.println("节点间距必须大于0");
            return false;
        }
        
        if (MAX_NODES <= 0 || MAX_EDGES <= 0) {
            System.err.println("最大节点数和边数必须大于0");
            return false;
        }
        
        if (MAX_PROCESSING_TIME <= 0) {
            System.err.println("最大处理时间必须大于0");
            return false;
        }
        
        return true;
    }
}
