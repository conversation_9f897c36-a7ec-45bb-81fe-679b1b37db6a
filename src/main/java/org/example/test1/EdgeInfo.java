package org.example.test1;

import com.fasterxml.jackson.databind.JsonNode;
import java.util.ArrayList;
import java.util.List;

/**
 * 边信息封装类
 * 用于存储BPM流程图中边的连接信息
 */
public class EdgeInfo {
    private String id;
    private String sourceId;
    private String targetId;
    private JsonNode originalEdge;
    private String value;

    // 路径信息
    private double sourceX;
    private double sourceY;
    private double targetX;
    private double targetY;
    private List<PathPoint> pathPoints = new ArrayList<>();

    public EdgeInfo() {
    }

    public EdgeInfo(String id, JsonNode originalEdge) {
        this.id = id;
        this.originalEdge = originalEdge;
        parseEdgeInfo();
    }

    /**
     * 从原始JSON边节点中解析边信息
     */
    private void parseEdgeInfo() {
        if (originalEdge == null) {
            return;
        }

        // 解析边的值/标签
        if (originalEdge.has("value")) {
            this.value = originalEdge.get("value").asText();
        }

        // 解析源节点ID
        if (originalEdge.has("source") && originalEdge.get("source").has("id")) {
            this.sourceId = originalEdge.get("source").get("id").asText();
        }

        // 解析目标节点ID
        if (originalEdge.has("target") && originalEdge.get("target").has("id")) {
            this.targetId = originalEdge.get("target").get("id").asText();
        }
    }

    /**
     * 检查边信息是否有效（必须有源节点和目标节点）
     */
    public boolean isValid() {
        return sourceId != null && !sourceId.isEmpty() && 
               targetId != null && !targetId.isEmpty();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public JsonNode getOriginalEdge() {
        return originalEdge;
    }

    public void setOriginalEdge(JsonNode originalEdge) {
        this.originalEdge = originalEdge;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    // 路径相关方法
    public void setSourcePoint(double x, double y) {
        this.sourceX = x;
        this.sourceY = y;
    }

    public void setTargetPoint(double x, double y) {
        this.targetX = x;
        this.targetY = y;
    }

    public double getSourceX() {
        return sourceX;
    }

    public double getSourceY() {
        return sourceY;
    }

    public double getTargetX() {
        return targetX;
    }

    public double getTargetY() {
        return targetY;
    }

    public void addPathPoint(double x, double y) {
        pathPoints.add(new PathPoint(x, y));
    }

    public void setPathPoints(List<PathPoint> points) {
        this.pathPoints = new ArrayList<>();
        for (PathPoint p : points) {
            this.pathPoints.add(new PathPoint(p.getX(), p.getY()));
        }
    }

    public List<PathPoint> getPathPoints() {
        return pathPoints;
    }

    public boolean hasPathInfo() {
        return !pathPoints.isEmpty();
    }

    // 兼容性方法
    public String getSource() {
        return sourceId;
    }

    public String getTarget() {
        return targetId;
    }

    @Override
    public String toString() {
        return "EdgeInfo{" +
                "id='" + id + '\'' +
                ", sourceId='" + sourceId + '\'' +
                ", targetId='" + targetId + '\'' +
                ", value='" + value + '\'' +
                ", pathPoints=" + pathPoints.size() +
                '}';
    }

    /**
     * 路径点类
     */
    public static class PathPoint {
        private double x;
        private double y;

        public PathPoint(double x, double y) {
            this.x = x;
            this.y = y;
        }

        public double getX() {
            return x;
        }

        public double getY() {
            return y;
        }

        @Override
        public String toString() {
            return "(" + x + "," + y + ")";
        }
    }
}
