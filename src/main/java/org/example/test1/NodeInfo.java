package org.example.test1;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * 节点信息封装类
 * 用于存储BPM流程图中节点的基本信息和几何属性
 */
public class NodeInfo {
    private String id;
    private String nodeType;
    private double x;
    private double y;
    private double width;
    private double height;
    private JsonNode originalNode;
    private String nodeName;
    private int nodeTypeInt;

    public NodeInfo() {
    }

    public NodeInfo(String id, JsonNode originalNode) {
        this.id = id;
        this.originalNode = originalNode;
        parseNodeInfo();
    }

    /**
     * 从原始JSON节点中解析节点信息
     */
    private void parseNodeInfo() {
        if (originalNode == null) {
            return;
        }

        // 解析节点类型
        if (originalNode.has("nodeType")) {
            this.nodeTypeInt = originalNode.get("nodeType").asInt();
            this.nodeType = String.valueOf(nodeTypeInt);
        }

        // 解析节点名称
        if (originalNode.has("nodeName")) {
            this.nodeName = originalNode.get("nodeName").asText();
        }

        // 解析几何信息
        if (originalNode.has("geometry")) {
            JsonNode geometry = originalNode.get("geometry");
            if (geometry.has("x")) {
                this.x = geometry.get("x").asDouble();
            }
            if (geometry.has("y")) {
                this.y = geometry.get("y").asDouble();
            }
            if (geometry.has("width")) {
                this.width = geometry.get("width").asDouble();
            }
            if (geometry.has("height")) {
                this.height = geometry.get("height").asDouble();
            }
        }
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }

    public double getWidth() {
        return width;
    }

    public void setWidth(double width) {
        this.width = width;
    }

    public double getHeight() {
        return height;
    }

    public void setHeight(double height) {
        this.height = height;
    }

    public JsonNode getOriginalNode() {
        return originalNode;
    }

    public void setOriginalNode(JsonNode originalNode) {
        this.originalNode = originalNode;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public int getNodeTypeInt() {
        return nodeTypeInt;
    }

    public void setNodeTypeInt(int nodeTypeInt) {
        this.nodeTypeInt = nodeTypeInt;
    }

    @Override
    public String toString() {
        return "NodeInfo{" +
                "id='" + id + '\'' +
                ", nodeType='" + nodeType + '\'' +
                ", nodeName='" + nodeName + '\'' +
                ", x=" + x +
                ", y=" + y +
                ", width=" + width +
                ", height=" + height +
                '}';
    }
}
