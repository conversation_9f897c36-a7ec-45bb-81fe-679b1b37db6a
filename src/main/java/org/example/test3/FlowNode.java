package org.example.test3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 流程节点实体类
 * 用于表示流程图中的节点信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowNode {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("nodeName")
    private String nodeName;
    
    @JsonProperty("nodeType")
    private Integer nodeType;
    
    @JsonProperty("geometry")
    private Geometry geometry;
    
    // 布局计算相关字段
    private int level = 0;           // 节点层级
    private int levelOrder = 0;      // 层级内排序
    private String parentId;         // 父节点ID
    private boolean isSpecialNode = false; // 是否为特殊节点（重新提交、byStart）
    
    public FlowNode() {}
    
    public FlowNode(String id, String nodeName, Integer nodeType) {
        this.id = id;
        this.nodeName = nodeName;
        this.nodeType = nodeType;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getNodeName() {
        return nodeName;
    }
    
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }
    
    public Integer getNodeType() {
        return nodeType;
    }
    
    public void setNodeType(Integer nodeType) {
        this.nodeType = nodeType;
    }
    
    public Geometry getGeometry() {
        return geometry;
    }
    
    public void setGeometry(Geometry geometry) {
        this.geometry = geometry;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public int getLevelOrder() {
        return levelOrder;
    }
    
    public void setLevelOrder(int levelOrder) {
        this.levelOrder = levelOrder;
    }
    
    public String getParentId() {
        return parentId;
    }
    
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
    
    public boolean isSpecialNode() {
        return isSpecialNode;
    }
    
    public void setSpecialNode(boolean specialNode) {
        isSpecialNode = specialNode;
    }
    
    /**
     * 判断是否为开始节点
     */
    public boolean isStartNode() {
        return nodeType != null && nodeType == 1;
    }
    
    /**
     * 判断是否为重新提交节点
     */
    public boolean isRecallNode() {
        return "recall".equals(id);
    }
    
    /**
     * 判断是否为byStart节点
     */
    public boolean isByStartNode() {
        return "byStart".equals(id);
    }
    
    /**
     * 获取节点类型优先级（用于排序）
     * 开始节点 > 任务节点 > 网关节点 > 结束节点
     */
    public int getNodeTypePriority() {
        if (nodeType == null) return 999;
        
        switch (nodeType) {
            case 1: return 1; // 开始节点
            case 2: return 2; // 初始化节点
            case 4: return 3; // 重新提交节点
            case 6: return 4; // 审核节点
            case 8: return 5; // 循环节点
            case 9: return 6; // 服务节点
            case 13: return 7; // 网关节点
            default: return 8; // 其他节点
        }
    }
    
    @Override
    public String toString() {
        return String.format("FlowNode{id='%s', nodeName='%s', nodeType=%d, level=%d, levelOrder=%d}", 
                id, nodeName, nodeType, level, levelOrder);
    }
    
    /**
     * 几何信息内部类
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Geometry {
        @JsonProperty("x")
        private double x;
        
        @JsonProperty("y")
        private double y;
        
        @JsonProperty("width")
        private double width;
        
        @JsonProperty("height")
        private double height;
        
        public Geometry() {}
        
        public Geometry(double x, double y, double width, double height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
        
        // Getter和Setter方法
        public double getX() {
            return x;
        }
        
        public void setX(double x) {
            this.x = x;
        }
        
        public double getY() {
            return y;
        }
        
        public void setY(double y) {
            this.y = y;
        }
        
        public double getWidth() {
            return width;
        }
        
        public void setWidth(double width) {
            this.width = width;
        }
        
        public double getHeight() {
            return height;
        }
        
        public void setHeight(double height) {
            this.height = height;
        }
        
        @Override
        public String toString() {
            return String.format("Geometry{x=%.1f, y=%.1f, width=%.1f, height=%.1f}", x, y, width, height);
        }
    }
}
