package org.example.test3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 流程连线实体类
 * 用于表示流程图中节点之间的连接关系
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowConnection {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("source")
    private NodeReference source;
    
    @JsonProperty("target")
    private NodeReference target;
    
    @JsonProperty("value")
    private String value;
    
    @JsonProperty("nodeType")
    private Integer nodeType;
    
    public FlowConnection() {}
    
    public FlowConnection(String id, NodeReference source, NodeReference target) {
        this.id = id;
        this.source = source;
        this.target = target;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public NodeReference getSource() {
        return source;
    }
    
    public void setSource(NodeReference source) {
        this.source = source;
    }
    
    public NodeReference getTarget() {
        return target;
    }
    
    public void setTarget(NodeReference target) {
        this.target = target;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public Integer getNodeType() {
        return nodeType;
    }
    
    public void setNodeType(Integer nodeType) {
        this.nodeType = nodeType;
    }
    
    /**
     * 判断是否为有效连线（有source和target）
     */
    public boolean isValidConnection() {
        return source != null && target != null && 
               source.getId() != null && target.getId() != null;
    }
    
    /**
     * 判断是否为重新提交连线（需要跳过的连线）
     */
    public boolean isRecallConnection() {
        return target != null && "recall".equals(target.getId());
    }
    
    /**
     * 获取源节点ID
     */
    public String getSourceId() {
        return source != null ? source.getId() : null;
    }
    
    /**
     * 获取目标节点ID
     */
    public String getTargetId() {
        return target != null ? target.getId() : null;
    }
    
    @Override
    public String toString() {
        return String.format("FlowConnection{id='%s', source='%s', target='%s', value='%s'}", 
                id, getSourceId(), getTargetId(), value);
    }
    
    /**
     * 节点引用内部类
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NodeReference {
        @JsonProperty("id")
        private String id;
        
        @JsonProperty("nodeName")
        private String nodeName;
        
        @JsonProperty("nodeType")
        private Integer nodeType;
        
        public NodeReference() {}
        
        public NodeReference(String id) {
            this.id = id;
        }
        
        public NodeReference(String id, String nodeName, Integer nodeType) {
            this.id = id;
            this.nodeName = nodeName;
            this.nodeType = nodeType;
        }
        
        // Getter和Setter方法
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getNodeName() {
            return nodeName;
        }
        
        public void setNodeName(String nodeName) {
            this.nodeName = nodeName;
        }
        
        public Integer getNodeType() {
            return nodeType;
        }
        
        public void setNodeType(Integer nodeType) {
            this.nodeType = nodeType;
        }
        
        @Override
        public String toString() {
            return String.format("NodeReference{id='%s', nodeName='%s', nodeType=%d}", 
                    id, nodeName, nodeType);
        }
    }
}
