package org.example.test3;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 流程图自动布局主程序
 * 
 * 功能说明：
 * 1. 解析processDesignModel.json文件中的流程节点和连线信息
 * 2. 基于流程图的逻辑关系，自动计算并更新所有节点的x、y坐标
 * 3. 实现清晰的层级布局，避免节点重叠和混乱排列
 * 4. 生成新的JSON文件和详细的处理报告
 * 
 * 处理步骤：
 * - 第一步：解析JSON结构并构建节点关系图
 * - 第二步：计算节点层级(level)和路径优化
 * - 第三步：层级内节点排序和编号
 * - 第四步：坐标计算和布局算法
 * - 第五步：生成输出文件和验证
 * 
 * 特殊处理：
 * - 开始节点：nodeType=1 的节点作为图遍历起点
 * - 重新提交连线：忽略 target_id='recall' 的连线，防止形成循环依赖
 * - 特殊节点：重新提交节点和 byStart 节点进行特殊的坐标偏移处理
 * 
 * 布局算法：
 * - X坐标：X = 100 + (level - 1) × 300
 * - Y坐标：单节点层级Y=280，多节点层级Y=200×level_order
 * - 特殊节点：X坐标减少1000px（向左偏移）
 */
public class FlowLayoutMain {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    private static final String OUTPUT_FILE = "src/main/java/org/example/test3/processDesignModel_updated.json";
    
    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("流程图自动布局处理程序");
        System.out.println("=".repeat(80));
        System.out.println("开始时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();
        
        try {
            // 验证输入文件是否存在
            File inputFile = new File(INPUT_FILE);
            if (!inputFile.exists()) {
                System.err.println("错误：输入文件不存在 - " + INPUT_FILE);
                System.err.println("请确保文件路径正确，并且文件存在。");
                return;
            }
            
            System.out.println("输入文件: " + INPUT_FILE);
            System.out.println("输出文件: " + OUTPUT_FILE);
            System.out.println("文件大小: " + formatFileSize(inputFile.length()));
            System.out.println();
            
            // 创建布局处理器并执行处理
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            processor.processFlowLayout(INPUT_FILE, OUTPUT_FILE);
            
            // 验证输出文件
            File outputFile = new File(OUTPUT_FILE);
            if (outputFile.exists()) {
                System.out.println();
                System.out.println("处理成功完成！");
                System.out.println("输出文件大小: " + formatFileSize(outputFile.length()));
                System.out.println("输出文件位置: " + outputFile.getAbsolutePath());
            } else {
                System.err.println("警告：输出文件未成功生成");
            }
            
        } catch (Exception e) {
            System.err.println("处理过程中发生错误:");
            System.err.println("错误类型: " + e.getClass().getSimpleName());
            System.err.println("错误信息: " + e.getMessage());
            e.printStackTrace();
        } finally {
            System.out.println();
            System.out.println("结束时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            System.out.println("=".repeat(80));
        }
    }
    
    /**
     * 格式化文件大小显示
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 显示使用说明
     */
    public static void showUsage() {
        System.out.println("流程图自动布局处理程序使用说明:");
        System.out.println();
        System.out.println("功能：");
        System.out.println("  - 自动分析流程图节点关系");
        System.out.println("  - 计算最优的节点布局坐标");
        System.out.println("  - 生成层级清晰的流程图布局");
        System.out.println("  - 避免节点重叠和混乱排列");
        System.out.println();
        System.out.println("输入文件要求：");
        System.out.println("  - JSON格式的流程图数据文件");
        System.out.println("  - 包含nodes数组（节点信息）");
        System.out.println("  - 包含lines数组（连线关系）");
        System.out.println("  - 每个节点包含id、nodeType、geometry等字段");
        System.out.println();
        System.out.println("输出结果：");
        System.out.println("  - 更新后的JSON文件（保持原始结构）");
        System.out.println("  - 重新计算的节点坐标");
        System.out.println("  - 详细的处理日志和统计报告");
        System.out.println();
        System.out.println("特殊处理：");
        System.out.println("  - 开始节点（nodeType=1）作为布局起点");
        System.out.println("  - 重新提交连线（target='recall'）被忽略");
        System.out.println("  - 特殊节点（recall、byStart）进行坐标偏移");
        System.out.println();
        System.out.println("布局算法：");
        System.out.println("  - 使用广度优先搜索(BFS)计算节点层级");
        System.out.println("  - X坐标 = 100 + (层级-1) × 300");
        System.out.println("  - Y坐标 = 单节点280，多节点200×序号");
        System.out.println("  - 特殊节点X坐标向左偏移1000px");
    }
}
