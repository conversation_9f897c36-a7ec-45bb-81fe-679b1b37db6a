package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.Iterator;
import java.util.Map;

/**
 * 流程图布局测试程序
 * 用于验证修改后的功能是否正常工作
 */
public class FlowLayoutTest {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    private static final String OUTPUT_FILE = "src/main/java/org/example/test3/processDesignModel_updated.json";
    
    public static void main(String[] args) {
        System.out.println("=".repeat(60));
        System.out.println("流程图布局测试程序");
        System.out.println("=".repeat(60));
        
        try {
            // 1. 运行布局处理
            System.out.println("1. 开始运行流程图布局处理...");
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            processor.processFlowLayout(INPUT_FILE, OUTPUT_FILE);
            
            // 2. 验证输出文件
            System.out.println("\n2. 验证输出文件...");
            validateOutputFile();
            
            // 3. 统计分析
            System.out.println("\n3. 统计分析...");
            analyzeFiles();
            
            System.out.println("\n测试完成！");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证输出文件
     */
    private static void validateOutputFile() {
        try {
            File outputFile = new File(OUTPUT_FILE);
            if (!outputFile.exists()) {
                System.err.println("❌ 输出文件不存在");
                return;
            }
            
            ObjectMapper mapper = new ObjectMapper();
            JsonNode outputJson = mapper.readTree(outputFile);
            
            int nodeCount = 0;
            int connectionCount = 0;
            int metadataCount = 0;
            
            Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (key.matches("\\d+")) {
                    metadataCount++;
                } else if (isNodeObject(value)) {
                    nodeCount++;
                } else if (isConnectionObject(value) || key.startsWith("line")) {
                    connectionCount++;
                }
            }
            
            System.out.println("✅ 输出文件验证结果:");
            System.out.println(String.format("   - 节点数量: %d", nodeCount));
            System.out.println(String.format("   - 连线数量: %d", connectionCount));
            System.out.println(String.format("   - 元数据对象: %d", metadataCount));
            System.out.println(String.format("   - 文件大小: %.2f KB", outputFile.length() / 1024.0));
            
            if (connectionCount == 0) {
                System.out.println("✅ 连线数据已成功移除");
            } else {
                System.out.println("⚠️  仍有连线数据存在，需要检查");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 验证输出文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 分析输入和输出文件
     */
    private static void analyzeFiles() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 分析输入文件
            File inputFile = new File(INPUT_FILE);
            JsonNode inputJson = mapper.readTree(inputFile);
            
            int inputNodes = 0, inputConnections = 0, inputMetadata = 0;
            Iterator<Map.Entry<String, JsonNode>> inputFields = inputJson.fields();
            while (inputFields.hasNext()) {
                Map.Entry<String, JsonNode> entry = inputFields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (key.matches("\\d+")) {
                    inputMetadata++;
                } else if (isNodeObject(value)) {
                    inputNodes++;
                } else if (isConnectionObject(value) || key.startsWith("line")) {
                    inputConnections++;
                }
            }
            
            // 分析输出文件
            File outputFile = new File(OUTPUT_FILE);
            JsonNode outputJson = mapper.readTree(outputFile);
            
            int outputNodes = 0, outputConnections = 0, outputMetadata = 0;
            Iterator<Map.Entry<String, JsonNode>> outputFields = outputJson.fields();
            while (outputFields.hasNext()) {
                Map.Entry<String, JsonNode> entry = outputFields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (key.matches("\\d+")) {
                    outputMetadata++;
                } else if (isNodeObject(value)) {
                    outputNodes++;
                } else if (isConnectionObject(value) || key.startsWith("line")) {
                    outputConnections++;
                }
            }
            
            System.out.println("📊 文件对比分析:");
            System.out.println("                    输入文件    输出文件    变化");
            System.out.println("   节点数量:        " + String.format("%8d", inputNodes) + 
                             String.format("%8d", outputNodes) + 
                             String.format("%8d", outputNodes - inputNodes));
            System.out.println("   连线数量:        " + String.format("%8d", inputConnections) + 
                             String.format("%8d", outputConnections) + 
                             String.format("%8d", outputConnections - inputConnections));
            System.out.println("   元数据对象:      " + String.format("%8d", inputMetadata) + 
                             String.format("%8d", outputMetadata) + 
                             String.format("%8d", outputMetadata - inputMetadata));
            System.out.println("   文件大小(KB):    " + String.format("%8.2f", inputFile.length() / 1024.0) + 
                             String.format("%8.2f", outputFile.length() / 1024.0) + 
                             String.format("%8.2f", (outputFile.length() - inputFile.length()) / 1024.0));
            
            // 计算压缩比例
            double compressionRatio = (double) outputFile.length() / inputFile.length() * 100;
            System.out.println(String.format("\n📈 文件压缩比例: %.1f%% (移除连线数据后)", compressionRatio));
            
        } catch (Exception e) {
            System.err.println("❌ 分析文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否为节点对象
     */
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    /**
     * 判断是否为连线对象
     */
    private static boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target")) ||
               value.has("abspoints");
    }
}
