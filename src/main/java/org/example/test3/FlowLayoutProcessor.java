package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程图布局处理器
 * 实现流程图的自动布局算法
 */
public class FlowLayoutProcessor {
    
    private static final int LEVEL_X_SPACING = 300;  // 层级间X轴间距
    private static final int BASE_X_OFFSET = 100;    // 基础X轴偏移
    private static final int SINGLE_NODE_Y = 280;    // 单节点层级Y坐标
    private static final int MULTI_NODE_Y_SPACING = 200; // 多节点层级Y轴间距
    private static final int SPECIAL_NODE_X_OFFSET = -1000; // 特殊节点X轴偏移
    
    private final ObjectMapper objectMapper;
    private Map<String, FlowNode> nodes;
    private List<FlowConnection> connections;
    private Map<Integer, List<FlowNode>> levelGroups;
    private List<String> processingLog;
    
    public FlowLayoutProcessor() {
        this.objectMapper = new ObjectMapper();
        this.nodes = new HashMap<>();
        this.connections = new ArrayList<>();
        this.levelGroups = new HashMap<>();
        this.processingLog = new ArrayList<>();
    }
    
    /**
     * 处理流程图布局
     */
    public void processFlowLayout(String inputFilePath, String outputFilePath) {
        try {
            log("开始处理流程图布局...");
            
            // 第一步：解析JSON文件
            parseJsonFile(inputFilePath);
            
            // 第二步：构建节点关系图
            buildNodeGraph();
            
            // 第三步：计算节点层级
            calculateNodeLevels();
            
            // 第四步：层级内排序
            sortNodesWithinLevels();
            
            // 第五步：计算坐标
            calculateCoordinates();
            
            // 第六步：生成输出文件
            generateOutputFile(inputFilePath, outputFilePath);
            
            // 第七步：生成处理报告
            generateProcessingReport();
            
            log("流程图布局处理完成！");
            
        } catch (Exception e) {
            log("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析JSON文件
     */
    private void parseJsonFile(String filePath) throws IOException {
        log("第一步：解析JSON文件结构...");

        JsonNode rootNode = objectMapper.readTree(new File(filePath));
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();

        int nodeCount = 0;
        int connectionCount = 0;
        int skippedCount = 0;

        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            // 跳过数字键和特殊键
            if (key.matches("\\d+") || "0".equals(key) || "1".equals(key)) {
                skippedCount++;
                continue;
            }

            // 解析节点 - 改进节点识别逻辑
            if (isNodeObject(value)) {
                FlowNode node = parseNode(key, value);
                if (node != null) {
                    nodes.put(key, node);
                    nodeCount++;
                    if (nodeCount <= 10) { // 只显示前10个节点的详细信息
                        log(String.format("解析节点: %s (%s) - 类型: %d",
                                key, node.getNodeName(), node.getNodeType()));
                    }
                }
            }
            // 解析连线 - 改进连线识别逻辑
            else if (isConnectionObject(value)) {
                FlowConnection connection = parseConnection(key, value);
                if (connection != null && connection.isValidConnection()) {
                    connections.add(connection);
                    connectionCount++;
                    if (connectionCount <= 10) { // 只显示前10条连线的详细信息
                        log(String.format("解析连线: %s -> %s",
                                connection.getSourceId(), connection.getTargetId()));
                    }
                }
            }
        }

        log(String.format("解析完成：共找到 %d 个节点，%d 条连线，跳过 %d 个对象",
                nodeCount, connectionCount, skippedCount));

        // 添加调试信息
        if (nodeCount > 10) {
            log(String.format("（节点详情仅显示前10个，实际共有 %d 个节点）", nodeCount));
        }
        if (connectionCount > 10) {
            log(String.format("（连线详情仅显示前10条，实际共有 %d 条连线）", connectionCount));
        }
    }

    /**
     * 判断是否为节点对象
     */
    private boolean isNodeObject(JsonNode value) {
        // 节点对象的特征：
        // 1. 有nodeType字段且不是连线
        // 2. 有geometry字段
        // 3. 没有edge字段或edge字段不为true
        return value.has("nodeType") &&
               value.has("geometry") &&
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }

    /**
     * 判断是否为连线对象
     */
    private boolean isConnectionObject(JsonNode value) {
        // 连线对象的特征：
        // 1. 有edge字段且为true，或者
        // 2. 有source和target字段，或者
        // 3. key以"line"开头
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    /**
     * 解析单个节点
     */
    private FlowNode parseNode(String key, JsonNode nodeJson) {
        try {
            FlowNode node = objectMapper.treeToValue(nodeJson, FlowNode.class);
            if (node.getId() == null) {
                node.setId(key);
            }
            
            // 标记特殊节点
            if (node.isRecallNode() || node.isByStartNode()) {
                node.setSpecialNode(true);
            }
            
            return node;
        } catch (Exception e) {
            log("解析节点失败: " + key + " - " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析单个连线
     */
    private FlowConnection parseConnection(String key, JsonNode connectionJson) {
        try {
            FlowConnection connection = objectMapper.treeToValue(connectionJson, FlowConnection.class);
            if (connection.getId() == null) {
                connection.setId(key);
            }
            return connection;
        } catch (Exception e) {
            log("解析连线失败: " + key + " - " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 构建节点关系图
     */
    private void buildNodeGraph() {
        log("第二步：构建节点关系图...");
        
        // 过滤掉重新提交连线
        List<FlowConnection> validConnections = connections.stream()
                .filter(conn -> !conn.isRecallConnection())
                .collect(Collectors.toList());
        
        log(String.format("过滤后有效连线数量: %d", validConnections.size()));
        
        // 为每个节点设置父节点信息
        for (FlowConnection connection : validConnections) {
            String sourceId = connection.getSourceId();
            String targetId = connection.getTargetId();
            
            FlowNode targetNode = nodes.get(targetId);
            if (targetNode != null && targetNode.getParentId() == null) {
                targetNode.setParentId(sourceId);
                log(String.format("设置父子关系: %s -> %s", sourceId, targetId));
            }
        }
    }
    
    /**
     * 计算节点层级
     */
    private void calculateNodeLevels() {
        log("第三步：计算节点层级...");
        
        // 找到开始节点
        FlowNode startNode = nodes.values().stream()
                .filter(FlowNode::isStartNode)
                .findFirst()
                .orElse(null);
        
        if (startNode == null) {
            log("警告：未找到开始节点，使用第一个节点作为起点");
            startNode = nodes.values().iterator().next();
        }
        
        // 使用BFS计算层级
        Queue<FlowNode> queue = new LinkedList<>();
        Set<String> visited = new HashSet<>();
        
        startNode.setLevel(1);
        queue.offer(startNode);
        visited.add(startNode.getId());
        
        log(String.format("从开始节点开始BFS遍历: %s", startNode.getId()));
        
        while (!queue.isEmpty()) {
            FlowNode currentNode = queue.poll();
            int currentLevel = currentNode.getLevel();
            
            // 找到所有子节点
            List<FlowConnection> outgoingConnections = connections.stream()
                    .filter(conn -> !conn.isRecallConnection())
                    .filter(conn -> currentNode.getId().equals(conn.getSourceId()))
                    .collect(Collectors.toList());
            
            for (FlowConnection connection : outgoingConnections) {
                String targetId = connection.getTargetId();
                FlowNode targetNode = nodes.get(targetId);
                
                if (targetNode != null && !visited.contains(targetId)) {
                    targetNode.setLevel(currentLevel + 1);
                    queue.offer(targetNode);
                    visited.add(targetId);
                    
                    log(String.format("设置节点层级: %s -> Level %d", 
                            targetId, currentLevel + 1));
                }
            }
        }
        
        // 处理未访问的节点（孤立节点）
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() == 0) {
                node.setLevel(999); // 孤立节点放在最后
                log(String.format("发现孤立节点: %s", node.getId()));
            }
        }
    }
    
    /**
     * 层级内节点排序
     */
    private void sortNodesWithinLevels() {
        log("第四步：层级内节点排序...");
        
        // 按层级分组
        levelGroups = nodes.values().stream()
                .collect(Collectors.groupingBy(FlowNode::getLevel));
        
        // 对每个层级内的节点进行排序
        for (Map.Entry<Integer, List<FlowNode>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<FlowNode> levelNodes = entry.getValue();
            
            // 排序规则：节点类型优先级 -> 节点名称字母顺序
            levelNodes.sort((n1, n2) -> {
                int typePriorityCompare = Integer.compare(n1.getNodeTypePriority(), n2.getNodeTypePriority());
                if (typePriorityCompare != 0) {
                    return typePriorityCompare;
                }
                
                String name1 = n1.getNodeName() != null ? n1.getNodeName() : n1.getId();
                String name2 = n2.getNodeName() != null ? n2.getNodeName() : n2.getId();
                return name1.compareTo(name2);
            });
            
            // 设置层级内排序
            for (int i = 0; i < levelNodes.size(); i++) {
                levelNodes.get(i).setLevelOrder(i + 1);
            }
            
            log(String.format("Level %d: %d 个节点已排序", level, levelNodes.size()));
        }
    }
    
    /**
     * 计算节点坐标
     */
    private void calculateCoordinates() {
        log("第五步：计算节点坐标...");

        for (Map.Entry<Integer, List<FlowNode>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<FlowNode> levelNodes = entry.getValue();

            // 计算X坐标
            double baseX = BASE_X_OFFSET + (level - 1) * LEVEL_X_SPACING;

            // 计算Y坐标
            if (levelNodes.size() == 1) {
                // 单节点层级，垂直居中
                FlowNode node = levelNodes.get(0);
                updateNodeCoordinates(node, baseX, SINGLE_NODE_Y);
            } else {
                // 多节点层级，等间距分布
                for (FlowNode node : levelNodes) {
                    double y = MULTI_NODE_Y_SPACING * node.getLevelOrder();
                    updateNodeCoordinates(node, baseX, y);
                }
            }

            log(String.format("Level %d 坐标计算完成，共 %d 个节点", level, levelNodes.size()));
        }

        // 处理特殊节点坐标调整
        adjustSpecialNodeCoordinates();
    }

    /**
     * 更新节点坐标
     */
    private void updateNodeCoordinates(FlowNode node, double x, double y) {
        if (node.getGeometry() == null) {
            node.setGeometry(new FlowNode.Geometry(x, y, 180, 110));
        } else {
            node.getGeometry().setX(x);
            node.getGeometry().setY(y);
        }

        log(String.format("更新节点坐标: %s -> (%.1f, %.1f)",
                node.getId(), x, y));
    }

    /**
     * 调整特殊节点坐标
     */
    private void adjustSpecialNodeCoordinates() {
        log("调整特殊节点坐标...");

        for (FlowNode node : nodes.values()) {
            if (node.isSpecialNode() && node.getGeometry() != null) {
                double currentX = node.getGeometry().getX();
                double newX = currentX + SPECIAL_NODE_X_OFFSET;
                node.getGeometry().setX(newX);

                log(String.format("特殊节点坐标调整: %s -> X坐标从 %.1f 调整为 %.1f",
                        node.getId(), currentX, newX));
            }
        }
    }

    /**
     * 生成输出文件
     */
    private void generateOutputFile(String inputFilePath, String outputFilePath) throws IOException {
        log("第六步：生成输出文件...");

        // 读取原始JSON文件
        JsonNode originalJson = objectMapper.readTree(new File(inputFilePath));
        ObjectNode outputJson = originalJson.deepCopy();

        // 更新节点坐标
        Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            FlowNode updatedNode = nodes.get(key);
            if (updatedNode != null && updatedNode.getGeometry() != null) {
                ObjectNode nodeObject = (ObjectNode) value;
                ObjectNode geometryObject = (ObjectNode) nodeObject.get("geometry");

                if (geometryObject != null) {
                    geometryObject.put("x", updatedNode.getGeometry().getX());
                    geometryObject.put("y", updatedNode.getGeometry().getY());

                    log(String.format("更新JSON中节点坐标: %s -> (%.1f, %.1f)",
                            key, updatedNode.getGeometry().getX(), updatedNode.getGeometry().getY()));
                }
            }
        }

        // 写入输出文件
        objectMapper.writerWithDefaultPrettyPrinter().writeValue(new File(outputFilePath), outputJson);
        log(String.format("输出文件已生成: %s", outputFilePath));
    }

    /**
     * 生成处理报告
     */
    private void generateProcessingReport() {
        log("\n========== 流程图布局处理报告 ==========");

        // 统计信息
        log(String.format("总节点数: %d", nodes.size()));
        log(String.format("总连线数: %d", connections.size()));
        log(String.format("层级数: %d", levelGroups.size()));

        // 层级分布统计
        log("\n层级分布统计:");
        for (Map.Entry<Integer, List<FlowNode>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<FlowNode> levelNodes = entry.getValue();
            log(String.format("  Level %d: %d 个节点", level, levelNodes.size()));
        }

        // 详细节点信息
        log("\n详细节点信息:");
        log(String.format("%-40s %-10s %-10s %-15s %-15s %-10s %-10s",
                "节点ID", "类型", "层级", "原X坐标", "原Y坐标", "新X坐标", "新Y坐标"));
        log("--------------------------------------------------------------------------------------------------------");

        for (FlowNode node : nodes.values()) {
            if (node.getGeometry() != null) {
                log(String.format("%-40s %-10d %-10d %-15s %-15s %-10.1f %-10.1f",
                        node.getId(),
                        node.getNodeType(),
                        node.getLevel(),
                        "N/A", // 原坐标信息在这里简化显示
                        "N/A",
                        node.getGeometry().getX(),
                        node.getGeometry().getY()));
            }
        }

        // 特殊节点处理记录
        log("\n特殊节点处理记录:");
        long specialNodeCount = nodes.values().stream()
                .filter(FlowNode::isSpecialNode)
                .count();
        log(String.format("特殊节点数量: %d", specialNodeCount));

        for (FlowNode node : nodes.values()) {
            if (node.isSpecialNode()) {
                log(String.format("  %s: %s", node.getId(),
                        node.isRecallNode() ? "重新提交节点" : "byStart节点"));
            }
        }

        log("\n========== 处理报告结束 ==========");
    }

    private void log(String message) {
        System.out.println(message);
        processingLog.add(message);
    }
}
