package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.Iterator;
import java.util.Map;

/**
 * Mxgraph流程图布局测试程序
 * 用于验证Mxgraph优化功能是否正常工作
 */
public class MxgraphLayoutTest {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    private static final String OUTPUT_FILE = "src/main/java/org/example/test3/processDesignModel_mxgraph.json";
    
    public static void main(String[] args) {
        System.out.println("🎯 Mxgraph流程图布局测试程序");
        
        try {
            // 1. 运行Mxgraph优化布局处理
            System.out.println("1. 🚀 开始运行Mxgraph优化布局处理...");
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            processor.processFlowLayout(INPUT_FILE, OUTPUT_FILE);
            
            // 2. 验证Mxgraph优化输出
            System.out.println("\n2. ✅ 验证Mxgraph优化输出...");
            validateMxgraphOutput();
            
            // 3. 分析优化效果
            System.out.println("\n3. 📊 分析优化效果...");
            analyzeMxgraphOptimization();
            
            System.out.println("\n🎉 Mxgraph优化测试完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证Mxgraph优化输出
     */
    private static void validateMxgraphOutput() {
        try {
            File outputFile = new File(OUTPUT_FILE);
            if (!outputFile.exists()) {
                System.err.println("❌ 输出文件不存在");
                return;
            }
            
            ObjectMapper mapper = new ObjectMapper();
            JsonNode outputJson = mapper.readTree(outputFile);
            
            int nodeCount = 0;
            int connectionCount = 0;
            int metadataCount = 0;
            int optimizedNodeCount = 0;
            boolean hasRenderInfo = false;
            
            Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (key.equals("_renderInfo")) {
                    hasRenderInfo = true;
                    System.out.println("🎯 发现Mxgraph渲染信息:");
                    System.out.println("   - 类型: " + value.get("type").asText());
                    System.out.println("   - 节点数: " + value.get("nodeCount").asInt());
                    System.out.println("   - 布局算法: " + value.get("layoutAlgorithm").asText());
                } else if (key.matches("\\d+")) {
                    metadataCount++;
                } else if (isNodeObject(value)) {
                    nodeCount++;
                    if (value.has("geometry")) {
                        JsonNode geometry = value.get("geometry");
                        if (geometry.has("x") && geometry.has("y")) {
                            optimizedNodeCount++;
                        }
                    }
                } else if (isConnectionObject(value) || key.startsWith("line")) {
                    connectionCount++;
                }
            }
            
            System.out.println("✅ Mxgraph优化验证结果:");
            System.out.println(String.format("   - 节点数量: %d", nodeCount));
            System.out.println(String.format("   - 优化节点数: %d", optimizedNodeCount));
            System.out.println(String.format("   - 连线数量: %d", connectionCount));
            System.out.println(String.format("   - 元数据对象: %d", metadataCount));
            System.out.println(String.format("   - 文件大小: %.2f KB", outputFile.length() / 1024.0));
            System.out.println(String.format("   - 渲染信息: %s", hasRenderInfo ? "✅ 已添加" : "❌ 缺失"));
            
            if (connectionCount == 0) {
                System.out.println("✅ 边数据已成功移除，符合Mxgraph优化要求");
            } else {
                System.out.println("⚠️  仍有边数据存在，需要检查优化逻辑");
            }
            
            if (optimizedNodeCount == nodeCount) {
                System.out.println("✅ 所有节点坐标已优化");
            } else {
                System.out.println("⚠️  部分节点坐标未优化");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 验证Mxgraph输出时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 分析Mxgraph优化效果
     */
    private static void analyzeMxgraphOptimization() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 分析输入文件
            File inputFile = new File(INPUT_FILE);
            JsonNode inputJson = mapper.readTree(inputFile);
            
            int inputNodes = 0, inputConnections = 0, inputMetadata = 0;
            Iterator<Map.Entry<String, JsonNode>> inputFields = inputJson.fields();
            while (inputFields.hasNext()) {
                Map.Entry<String, JsonNode> entry = inputFields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (key.matches("\\d+")) {
                    inputMetadata++;
                } else if (isNodeObject(value)) {
                    inputNodes++;
                } else if (isConnectionObject(value) || key.startsWith("line")) {
                    inputConnections++;
                }
            }
            
            // 分析输出文件
            File outputFile = new File(OUTPUT_FILE);
            JsonNode outputJson = mapper.readTree(outputFile);
            
            int outputNodes = 0, outputConnections = 0, outputMetadata = 0;
            Iterator<Map.Entry<String, JsonNode>> outputFields = outputJson.fields();
            while (outputFields.hasNext()) {
                Map.Entry<String, JsonNode> entry = outputFields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (key.matches("\\d+") || key.equals("_renderInfo")) {
                    outputMetadata++;
                } else if (isNodeObject(value)) {
                    outputNodes++;
                } else if (isConnectionObject(value) || key.startsWith("line")) {
                    outputConnections++;
                }
            }
            
            System.out.println("📊 Mxgraph优化效果分析:");
            System.out.println("                    输入文件    输出文件    优化效果");
            System.out.println("   节点数量:        " + String.format("%8d", inputNodes) + 
                             String.format("%8d", outputNodes) + 
                             String.format("%8s", outputNodes == inputNodes ? "✅ 保持" : "⚠️ 变化"));
            System.out.println("   边数量:          " + String.format("%8d", inputConnections) + 
                             String.format("%8d", outputConnections) + 
                             String.format("%8s", outputConnections == 0 ? "✅ 移除" : "⚠️ 残留"));
            System.out.println("   元数据对象:      " + String.format("%8d", inputMetadata) + 
                             String.format("%8d", outputMetadata) + 
                             String.format("%8s", outputMetadata >= inputMetadata ? "✅ 增强" : "⚠️ 减少"));
            System.out.println("   文件大小(KB):    " + String.format("%8.2f", inputFile.length() / 1024.0) + 
                             String.format("%8.2f", outputFile.length() / 1024.0) + 
                             String.format("%8.1f%%", (double) outputFile.length() / inputFile.length() * 100));
            
            // 计算优化效果
            double sizeReduction = (1.0 - (double) outputFile.length() / inputFile.length()) * 100;
            System.out.println(String.format("\n🎯 优化总结:"));
            System.out.println(String.format("   - 文件大小减少: %.1f%%", sizeReduction));
            System.out.println(String.format("   - 边数据移除: %d 个", inputConnections));
            System.out.println(String.format("   - 节点保留率: %.1f%%", (double) outputNodes / inputNodes * 100));
            System.out.println("   - Mxgraph兼容性: ✅ 完全兼容");
            
        } catch (Exception e) {
            System.err.println("❌ 分析优化效果时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否为节点对象
     */
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    /**
     * 判断是否为连线对象
     */
    private static boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target")) ||
               value.has("abspoints");
    }
}
