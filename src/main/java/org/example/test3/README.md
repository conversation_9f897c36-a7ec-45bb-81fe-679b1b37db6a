# 流程图自动布局处理程序

## 概述

这是一个Java程序，用于自动处理流程图的布局，基于流程图的逻辑关系自动计算并更新所有节点的坐标，实现清晰的层级布局，避免节点重叠和混乱排列。

## 功能特性

### 核心功能
- **自动解析JSON格式的流程图数据**
- **智能计算节点层级关系**
- **优化节点布局坐标**
- **生成层级清晰的流程图**
- **避免节点重叠和混乱排列**

### 特殊处理
- **开始节点识别**：自动识别 `nodeType=1` 的节点作为布局起点
- **循环依赖处理**：忽略 `target_id='recall'` 的连线，防止形成循环依赖
- **特殊节点偏移**：重新提交节点和 byStart 节点进行特殊的坐标偏移处理

## 技术实现

### 处理步骤

1. **第一步：解析JSON结构并构建节点关系图**
   - 读取并解析 `processDesignModel.json` 文件
   - 提取 `nodes` 数组（包含所有节点信息）
   - 提取 `lines` 数组（包含连线关系）
   - 构建有向图数据结构

2. **第二步：计算节点层级(level)和路径优化**
   - 从开始节点开始，使用广度优先搜索(BFS)算法遍历整个流程图
   - 层级计算规则：开始节点 level = 1，其他节点 level = max(所有父节点的level) + 1
   - 去重和路径优化：选择level值最小的路径（最短路径优先）

3. **第三步：层级内节点排序和编号**
   - 按 level 值对所有节点进行分组
   - 在每个 level 组内按节点类型优先级和名称排序
   - 为每个 level 内的节点分配 `level_order`

4. **第四步：坐标计算和布局算法**
   - **X坐标计算：** `X = 100 + (level - 1) × 300`
   - **Y坐标计算：** 单节点层级 `Y = 280`，多节点层级 `Y = 200 × level_order`
   - **特殊节点调整：** 重新提交节点和byStart节点 X坐标减少1000px

5. **第五步：生成输出文件和验证**
   - 创建原JSON文件的深拷贝
   - 仅更新节点的 `x`、`y` 坐标值
   - 保存为新文件并生成处理报告

### 布局算法参数

```java
private static final int LEVEL_X_SPACING = 300;      // 层级间X轴间距
private static final int BASE_X_OFFSET = 100;        // 基础X轴偏移
private static final int SINGLE_NODE_Y = 280;        // 单节点层级Y坐标
private static final int MULTI_NODE_Y_SPACING = 200; // 多节点层级Y轴间距
private static final int SPECIAL_NODE_X_OFFSET = -1000; // 特殊节点X轴偏移
```

## 使用方法

### 环境要求
- Java 8 或更高版本
- Maven 3.6 或更高版本

### 编译和运行

1. **编译项目**
```bash
mvn clean compile
```

2. **运行测试**
```bash
mvn test
```

3. **运行主程序**
```bash
mvn exec:java -Dexec.mainClass="org.example.test3.FlowLayoutMain"
```

或者直接运行：
```bash
java -cp target/classes org.example.test3.FlowLayoutMain
```

### 输入文件格式

输入文件 `processDesignModel.json` 应包含以下结构：

```json
{
  "节点ID": {
    "id": "节点ID",
    "nodeName": "节点名称",
    "nodeType": 节点类型数字,
    "geometry": {
      "x": X坐标,
      "y": Y坐标,
      "width": 宽度,
      "height": 高度
    }
  },
  "连线ID": {
    "id": "连线ID",
    "edge": "true",
    "source": {
      "id": "源节点ID"
    },
    "target": {
      "id": "目标节点ID"
    }
  }
}
```

### 输出结果

程序会生成以下输出：

1. **更新后的JSON文件**：`processDesignModel_updated.json`
   - 包含重新计算坐标的完整流程图数据
   - 保持原始JSON文件的所有非坐标数据完整性

2. **详细处理日志**：控制台输出
   - 每个处理步骤的详细信息
   - 节点解析和坐标计算过程
   - 特殊节点处理记录

3. **布局验证报告**：
   - 层级分布统计
   - 每个节点的详细信息表格
   - 特殊节点处理记录
   - 处理异常和警告信息

## 项目结构

```
src/main/java/org/example/test3/
├── FlowNode.java              # 流程节点实体类
├── FlowConnection.java        # 流程连线实体类
├── FlowLayoutProcessor.java   # 布局处理器核心类
├── FlowLayoutMain.java        # 主程序入口
├── processDesignModel.json    # 输入文件（流程图数据）
└── README.md                  # 说明文档

src/test/java/org/example/test3/
└── FlowLayoutProcessorTest.java # 单元测试类
```

## 节点类型说明

| nodeType | 说明 | 优先级 |
|----------|------|--------|
| 1 | 开始节点 | 1 |
| 2 | 初始化节点 | 2 |
| 4 | 重新提交节点 | 3 |
| 6 | 审核节点 | 4 |
| 8 | 循环节点 | 5 |
| 9 | 服务节点 | 6 |
| 13 | 网关节点 | 7 |

## 特殊节点处理

### 重新提交节点 (recall)
- **识别条件**：节点ID为 "recall"
- **处理方式**：X坐标向左偏移1000px
- **连线处理**：忽略指向recall节点的连线，防止循环依赖

### byStart节点
- **识别条件**：节点ID为 "byStart"
- **处理方式**：X坐标向左偏移1000px
- **用途**：通常作为流程的辅助起点

## 错误处理

程序包含完善的错误处理机制：

- **文件不存在**：检查输入文件是否存在并可读
- **JSON解析错误**：捕获并报告JSON格式错误
- **节点关系异常**：处理孤立节点和循环依赖
- **坐标计算异常**：验证坐标合理性并提供默认值

## 验收标准

✅ **布局清晰**：生成的流程图布局清晰，层级分明  
✅ **无重叠**：无节点坐标重叠或异常位置  
✅ **数据完整**：保持原始JSON文件的所有非坐标数据完整性  
✅ **特殊处理**：重新提交、byStart节点正确偏移  
✅ **日志完整**：处理日志完整且易于理解  

## 许可证

本项目采用 MIT 许可证。
