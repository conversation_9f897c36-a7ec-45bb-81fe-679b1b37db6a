package org.example.test0;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Professional BPMN Flowchart Layout Optimization Engine
 * 
 * Based on BPMN 2.0 standards, implements professional flowchart automatic layout algorithms:
 * 1. Intelligent layering algorithm based on topological sorting
 * 2. Barycenter method node ordering and crossing minimization
 * 3. Orthogonal connection line routing system (L-shape, Z-shape, U-shape)
 * 4. A* algorithm path planning and conflict avoidance
 * 5. Grid snapping and precise alignment
 * 6. BPMN gateway specialized layout processing
 *
 * <AUTHOR> Assistant
 * @version 2.0 - Professional BPMN Layout Engine
 */
public class BPMFlowchartLayoutOptimizer {

    // BPMN specialized layout configuration parameters
    private static final int HORIZONTAL_SPACING = 180;     // Horizontal spacing
    private static final int VERTICAL_SPACING = 120;       // Vertical spacing
    private static final int GATEWAY_BRANCH_SPACING = 80;  // Gateway branch spacing
    private static final int CONNECTION_MARGIN = 20;       // Connection line margin
    private static final boolean ENABLE_ORTHOGONAL_ROUTING = true; // Enable orthogonal routing
    private static final int GRID_SIZE = 10;               // Grid snapping size
    private static final int MIN_NODE_DISTANCE = 50;       // Minimum node distance

    // BPMN node type constants
    private static final int NODE_TYPE_START = 1;          // Start node
    private static final int NODE_TYPE_INIT = 2;           // Initialization node
    private static final int NODE_TYPE_RECALL = 4;         // Resubmit node
    private static final int NODE_TYPE_APPROVAL = 6;       // Approval node
    private static final int NODE_TYPE_LOOP = 8;           // Loop node
    private static final int NODE_TYPE_SERVICE = 9;        // Service node
    private static final int NODE_TYPE_GATEWAY = 13;       // Gateway node
    private static final int NODE_TYPE_EDGE = 99;          // Connection line

    // Connection line routing types
    private enum RouteType {
        STRAIGHT,    // Straight connection
        L_SHAPE,     // L-shaped connection
        Z_SHAPE,     // Z-shaped connection
        U_SHAPE      // U-shaped connection
    }

    /**
     * Two-dimensional coordinate point
     */
    public static class Point {
        public double x;
        public double y;

        public Point(double x, double y) {
            this.x = x;
            this.y = y;
        }

        /**
         * Grid snapping - align coordinates to specified grid
         */
        public Point snapToGrid(int gridSize) {
            return new Point(
                Math.round(x / gridSize) * gridSize,
                Math.round(y / gridSize) * gridSize
            );
        }

        /**
         * Calculate distance to another point
         */
        public double distanceTo(Point other) {
            double dx = x - other.x;
            double dy = y - other.y;
            return Math.sqrt(dx * dx + dy * dy);
        }

        @Override
        public String toString() {
            return String.format("(%.1f, %.1f)", x, y);
        }
    }

    /**
     * BPMN node information
     */
    public static class BPMNNode {
        public String id;
        public int nodeType;
        public String nodeName;
        
        // Geometric information
        public double originalX;
        public double originalY;
        public double width;
        public double height;
        
        // Layout calculation results
        public Point layoutPosition;
        public int layer = -1;              // Layering result
        public int orderInLayer = -1;       // Order within layer
        public double barycenter = 0.0;     // Barycenter value
        
        // Graph structure relationships
        public List<BPMNEdge> incomingEdges = new ArrayList<>();
        public List<BPMNEdge> outgoingEdges = new ArrayList<>();
        
        // BPMN special attributes
        public boolean isGateway = false;
        public boolean isStartNode = false;
        public boolean isEndNode = false;

        public BPMNNode(String id) {
            this.id = id;
        }

        /**
         * Get node center point
         */
        public Point getCenter() {
            if (layoutPosition != null) {
                return new Point(layoutPosition.x + width / 2, layoutPosition.y + height / 2);
            }
            return new Point(originalX + width / 2, originalY + height / 2);
        }

        /**
         * Get node connection points (top, bottom, left, right directions)
         */
        public Point getConnectionPoint(String direction) {
            Point pos = layoutPosition != null ? layoutPosition : new Point(originalX, originalY);
            switch (direction.toLowerCase()) {
                case "top":
                    return new Point(pos.x + width / 2, pos.y);
                case "bottom":
                    return new Point(pos.x + width / 2, pos.y + height);
                case "left":
                    return new Point(pos.x, pos.y + height / 2);
                case "right":
                    return new Point(pos.x + width, pos.y + height / 2);
                default:
                    return getCenter();
            }
        }

        /**
         * Check if node overlaps with another node
         */
        public boolean overlapsWith(BPMNNode other) {
            Point thisPos = layoutPosition != null ? layoutPosition : new Point(originalX, originalY);
            Point otherPos = other.layoutPosition != null ? other.layoutPosition : new Point(other.originalX, other.originalY);
            
            return !(thisPos.x + width < otherPos.x || 
                    otherPos.x + other.width < thisPos.x ||
                    thisPos.y + height < otherPos.y || 
                    otherPos.y + other.height < thisPos.y);
        }

        @Override
        public String toString() {
            return String.format("BPMNNode[%s, type=%d, layer=%d, order=%d]", 
                id, nodeType, layer, orderInLayer);
        }
    }

    /**
     * BPMN connection line information
     */
    public static class BPMNEdge {
        public String id;
        public String sourceId;
        public String targetId;
        public String value;  // Connection line label (such as conditions)
        
        public BPMNNode sourceNode;
        public BPMNNode targetNode;
        
        // Routing calculation results
        public List<Point> routePoints = new ArrayList<>();
        public RouteType routeType = RouteType.STRAIGHT;
        public double totalLength = 0.0;
        public int crossingCount = 0;
        
        // Connection point information
        public String sourceConnectionSide = "bottom";  // Source node connection side
        public String targetConnectionSide = "top";     // Target node connection side
        
        // Special attributes
        public boolean isBackEdge = false;      // Whether it's a back edge (forming a loop)
        public boolean isLongDistance = false;  // Whether it's a long-distance connection
        public int priority = 0;                // Routing priority

        public BPMNEdge(String id, String sourceId, String targetId) {
            this.id = id;
            this.sourceId = sourceId;
            this.targetId = targetId;
        }

        /**
         * Calculate total length of connection line
         */
        public double calculateTotalLength() {
            if (routePoints.size() < 2) return 0.0;
            
            double length = 0.0;
            for (int i = 0; i < routePoints.size() - 1; i++) {
                length += routePoints.get(i).distanceTo(routePoints.get(i + 1));
            }
            return length;
        }

        /**
         * Check if connection line crosses with another connection line
         */
        public boolean crossesWith(BPMNEdge other) {
            if (routePoints.size() < 2 || other.routePoints.size() < 2) {
                return false;
            }
            
            // Simplified crossing detection: check if line segments intersect
            for (int i = 0; i < routePoints.size() - 1; i++) {
                for (int j = 0; j < other.routePoints.size() - 1; j++) {
                    if (lineSegmentsIntersect(
                        routePoints.get(i), routePoints.get(i + 1),
                        other.routePoints.get(j), other.routePoints.get(j + 1))) {
                        return true;
                    }
                }
            }
            return false;
        }

        /**
         * Check if two line segments intersect
         */
        private boolean lineSegmentsIntersect(Point p1, Point q1, Point p2, Point q2) {
            // Use vector cross product to determine line segment intersection
            double d1 = direction(p2, q2, p1);
            double d2 = direction(p2, q2, q1);
            double d3 = direction(p1, q1, p2);
            double d4 = direction(p1, q1, q2);
            
            if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
                ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
                return true;
            }
            return false;
        }

        private double direction(Point pi, Point pj, Point pk) {
            return (pk.x - pi.x) * (pj.y - pi.y) - (pj.x - pi.x) * (pk.y - pi.y);
        }

        @Override
        public String toString() {
            return String.format("BPMNEdge[%s: %s -> %s, type=%s]", 
                id, sourceId, targetId, routeType);
        }
    }

    /**
     * BPMN graph structure
     */
    public static class BPMNGraph {
        public Map<String, BPMNNode> nodes = new HashMap<>();
        public Map<String, BPMNEdge> edges = new HashMap<>();
        public List<List<BPMNNode>> layers = new ArrayList<>();
        
        /**
         * Add node
         */
        public void addNode(BPMNNode node) {
            nodes.put(node.id, node);
        }
        
        /**
         * Add connection line
         */
        public void addEdge(BPMNEdge edge) {
            edges.put(edge.id, edge);
            
            // Establish connection relationships between nodes
            BPMNNode sourceNode = nodes.get(edge.sourceId);
            BPMNNode targetNode = nodes.get(edge.targetId);
            
            if (sourceNode != null && targetNode != null) {
                edge.sourceNode = sourceNode;
                edge.targetNode = targetNode;
                sourceNode.outgoingEdges.add(edge);
                targetNode.incomingEdges.add(edge);
            }

    /**
     * BPMN Layout Engine - Core algorithm implementation
     */
    public static class BPMNLayoutEngine {
        private BPMNGraph graph;

        public BPMNLayoutEngine(BPMNGraph graph) {
            this.graph = graph;
        }

        /**
         * Execute complete layout algorithm
         */
        public void executeLayout() {
            System.out.println("Starting BPMN professional layout algorithm...");

            // 1. Preprocessing: identify node types and special attributes
            preprocessNodes();

            // 2. Layering algorithm: node layering based on topological sorting
            layerNodes();

            // 3. Node ordering: barycenter method sorting and crossing minimization
            orderNodesInLayers();

            // 4. Coordinate assignment: calculate precise node coordinates
            assignCoordinates();

            // 5. Edge routing: orthogonal routing and path optimization
            routeEdges();

            // 6. Post-processing: grid snapping and conflict resolution
            postProcessLayout();

            System.out.println("BPMN layout algorithm execution completed");
        }

        /**
         * Preprocessing: identify node types and set special attributes
         */
        private void preprocessNodes() {
            System.out.println("Preprocessing node types...");

            for (BPMNNode node : graph.nodes.values()) {
                // Identify special node types
                switch (node.nodeType) {
                    case NODE_TYPE_START:
                        node.isStartNode = true;
                        break;
                    case NODE_TYPE_GATEWAY:
                        node.isGateway = true;
                        break;
                }

                // Identify end nodes (nodes with no outgoing edges)
                if (node.outgoingEdges.isEmpty() && !node.isStartNode) {
                    node.isEndNode = true;
                }
            }

            System.out.println("Preprocessing completed - Start nodes: " +
                graph.nodes.values().stream().mapToInt(n -> n.isStartNode ? 1 : 0).sum() +
                ", Gateway nodes: " +
                graph.nodes.values().stream().mapToInt(n -> n.isGateway ? 1 : 0).sum());
        }

        /**
         * Layering algorithm: node layering based on topological sorting
         */
        private void layerNodes() {
            System.out.println("Starting node layering...");

            // Initialize all node layers to -1
            for (BPMNNode node : graph.nodes.values()) {
                node.layer = -1;
            }

            // Step 1: Use topological sorting to determine basic layers
            Queue<BPMNNode> queue = new LinkedList<>();
            Map<String, Integer> inDegree = new HashMap<>();

            // Calculate in-degree
            for (BPMNNode node : graph.nodes.values()) {
                inDegree.put(node.id, node.incomingEdges.size());
                if (node.incomingEdges.isEmpty()) {
                    queue.offer(node);
                    node.layer = 0;  // Start nodes are in layer 0
                }
            }

            // Topological sorting layering
            while (!queue.isEmpty()) {
                BPMNNode current = queue.poll();

                for (BPMNEdge edge : current.outgoingEdges) {
                    BPMNNode target = edge.targetNode;

                    // Update target node layer
                    target.layer = Math.max(target.layer, current.layer + 1);

                    // Decrease in-degree
                    int newInDegree = inDegree.get(target.id) - 1;
                    inDegree.put(target.id, newInDegree);

                    if (newInDegree == 0) {
                        queue.offer(target);
                    }
                }
            }

            // Step 2: Build layer structure
            buildLayerStructure();

            System.out.println("Layering completed - Total layers: " + graph.layers.size());
            for (int i = 0; i < graph.layers.size(); i++) {
                System.out.println("  Layer " + i + ": " + graph.layers.get(i).size() + " nodes");
            }
        }

        /**
         * Build layer structure
         */
        private void buildLayerStructure() {
            // Find maximum layer
            int maxLayer = graph.nodes.values().stream()
                .mapToInt(node -> node.layer)
                .max()
                .orElse(0);

            // Initialize layer list
            graph.layers.clear();
            for (int i = 0; i <= maxLayer; i++) {
                graph.layers.add(new ArrayList<>());
            }

            // Assign nodes to corresponding layers
            for (BPMNNode node : graph.nodes.values()) {
                if (node.layer >= 0 && node.layer <= maxLayer) {
                    graph.layers.get(node.layer).add(node);
                }
            }
        }

        /**
         * Node ordering: barycenter method sorting and crossing minimization
         */
        private void orderNodesInLayers() {
            System.out.println("Starting node ordering...");

            // Multiple iterations to optimize node order
            for (int iteration = 0; iteration < 5; iteration++) {
                boolean improved = false;

                // Optimize from top to bottom
                for (int layer = 1; layer < graph.layers.size(); layer++) {
                    if (optimizeLayerOrder(layer, true)) {
                        improved = true;
                    }
                }

                // Optimize from bottom to top
                for (int layer = graph.layers.size() - 2; layer >= 0; layer--) {
                    if (optimizeLayerOrder(layer, false)) {
                        improved = true;
                    }
                }

                if (!improved) {
                    System.out.println("Ordering optimization converged after " + (iteration + 1) + " iterations");
                    break;
                }
            }

            // Set node order index within layer
            for (int layer = 0; layer < graph.layers.size(); layer++) {
                List<BPMNNode> layerNodes = graph.layers.get(layer);
                for (int i = 0; i < layerNodes.size(); i++) {
                    layerNodes.get(i).orderInLayer = i;
                }
            }

            System.out.println("Node ordering completed");
        }

        /**
         * Optimize single layer node order (barycenter method)
         */
        private boolean optimizeLayerOrder(int layerIndex, boolean downward) {
            List<BPMNNode> layer = graph.layers.get(layerIndex);
            if (layer.size() <= 1) return false;

            // Calculate barycenter value for each node
            for (BPMNNode node : layer) {
                node.barycenter = calculateBarycenter(node, downward);
            }

            // Sort by barycenter value
            List<BPMNNode> originalOrder = new ArrayList<>(layer);
            layer.sort(Comparator.comparingDouble(node -> node.barycenter));

            // Check if there's improvement
            return !originalOrder.equals(layer);
        }

        /**
         * Calculate node barycenter value
         */
        private double calculateBarycenter(BPMNNode node, boolean downward) {
            List<BPMNEdge> relevantEdges = downward ? node.incomingEdges : node.outgoingEdges;

            if (relevantEdges.isEmpty()) {
                return node.orderInLayer >= 0 ? node.orderInLayer : 0;
            }

            double sum = 0.0;
            int count = 0;

            for (BPMNEdge edge : relevantEdges) {
                BPMNNode connectedNode = downward ? edge.sourceNode : edge.targetNode;
                if (connectedNode != null && connectedNode.orderInLayer >= 0) {
                    sum += connectedNode.orderInLayer;
                    count++;
                }
            }

            return count > 0 ? sum / count : 0.0;
        }

        /**
         * Coordinate assignment: calculate precise node coordinates
         */
        private void assignCoordinates() {
            System.out.println("Starting coordinate assignment...");

            double currentY = VERTICAL_SPACING;  // Starting Y coordinate

            for (int layerIndex = 0; layerIndex < graph.layers.size(); layerIndex++) {
                List<BPMNNode> layer = graph.layers.get(layerIndex);

                // Calculate total width of this layer
                double totalWidth = calculateLayerWidth(layer);
                double startX = -totalWidth / 2;  // Center alignment

                double currentX = startX;

                for (BPMNNode node : layer) {
                    // Calculate node position (grid snapping)
                    Point position = new Point(currentX, currentY).snapToGrid(GRID_SIZE);
                    node.layoutPosition = position;

                    // Update X coordinate
                    currentX += node.width + HORIZONTAL_SPACING;
                }

                // Update Y coordinate to next layer
                double layerHeight = layer.stream()
                    .mapToDouble(node -> node.height)
                    .max()
                    .orElse(100);
                currentY += layerHeight + VERTICAL_SPACING;
            }

            System.out.println("Coordinate assignment completed");
        }

        /**
         * Calculate total width of layer
         */
        private double calculateLayerWidth(List<BPMNNode> layer) {
            if (layer.isEmpty()) return 0;

            double totalWidth = 0;
            for (int i = 0; i < layer.size(); i++) {
                totalWidth += layer.get(i).width;
                if (i < layer.size() - 1) {
                    totalWidth += HORIZONTAL_SPACING;
                }
            }
            return totalWidth;
        }

        /**
         * Edge routing: orthogonal routing and path optimization
         */
        private void routeEdges() {
            System.out.println("Starting edge routing...");

            for (BPMNEdge edge : graph.edges.values()) {
                routeSingleEdge(edge);
            }

            // Optimize edge crossings
            optimizeEdgeCrossings();

            System.out.println("Edge routing completed");
        }

        /**
         * Route single edge
         */
        private void routeSingleEdge(BPMNEdge edge) {
            BPMNNode source = edge.sourceNode;
            BPMNNode target = edge.targetNode;

            if (source == null || target == null) return;

            // Determine connection points
            determineConnectionPoints(edge);

            // Choose routing type based on node position relationship
            if (isDirectConnection(source, target)) {
                createStraightRoute(edge);
            } else if (isAdjacentLayers(source, target)) {
                createLShapeRoute(edge);
            } else {
                createZShapeRoute(edge);
            }

            // Calculate route length
            edge.totalLength = edge.calculateTotalLength();
        }

        /**
         * Determine connection point positions
         */
        private void determineConnectionPoints(BPMNEdge edge) {
            BPMNNode source = edge.sourceNode;
            BPMNNode target = edge.targetNode;

            // Determine connection side based on node layer relationship
            if (source.layer < target.layer) {
                // Forward connection: from bottom to top
                edge.sourceConnectionSide = "bottom";
                edge.targetConnectionSide = "top";
            } else if (source.layer > target.layer) {
                // Reverse connection: from top to bottom (back edge)
                edge.sourceConnectionSide = "right";
                edge.targetConnectionSide = "left";
                edge.isBackEdge = true;
            } else {
                // Same layer connection: from right to left
                edge.sourceConnectionSide = "right";
                edge.targetConnectionSide = "left";
            }
        }
        }
        
        /**
         * Get all start nodes
         */
        public List<BPMNNode> getStartNodes() {
            return nodes.values().stream()
                .filter(node -> node.nodeType == NODE_TYPE_START || node.incomingEdges.isEmpty())
                .collect(Collectors.toList());
        }
        
        /**
         * Get all end nodes
         */
        public List<BPMNNode> getEndNodes() {
            return nodes.values().stream()
                .filter(node -> node.outgoingEdges.isEmpty())
                .collect(Collectors.toList());
        }

        /**
         * Check if it's a direct connection
         */
        private boolean isDirectConnection(BPMNNode source, BPMNNode target) {
            return Math.abs(source.layer - target.layer) <= 1 &&
                   Math.abs(source.orderInLayer - target.orderInLayer) <= 1;
        }

        /**
         * Check if it's an adjacent layer connection
         */
        private boolean isAdjacentLayers(BPMNNode source, BPMNNode target) {
            return Math.abs(source.layer - target.layer) == 1;
        }

        /**
         * Create straight route
         */
        private void createStraightRoute(BPMNEdge edge) {
            edge.routeType = RouteType.STRAIGHT;
            edge.routePoints.clear();

            Point sourcePoint = edge.sourceNode.getConnectionPoint(edge.sourceConnectionSide);
            Point targetPoint = edge.targetNode.getConnectionPoint(edge.targetConnectionSide);

            edge.routePoints.add(sourcePoint);
            edge.routePoints.add(targetPoint);
        }

        /**
         * Create L-shaped route
         */
        private void createLShapeRoute(BPMNEdge edge) {
            edge.routeType = RouteType.L_SHAPE;
            edge.routePoints.clear();

            Point sourcePoint = edge.sourceNode.getConnectionPoint(edge.sourceConnectionSide);
            Point targetPoint = edge.targetNode.getConnectionPoint(edge.targetConnectionSide);

            edge.routePoints.add(sourcePoint);

            // Add turning point
            if (edge.sourceConnectionSide.equals("bottom") && edge.targetConnectionSide.equals("top")) {
                // Vertical L-shape
                double midY = (sourcePoint.y + targetPoint.y) / 2;
                edge.routePoints.add(new Point(sourcePoint.x, midY));
                edge.routePoints.add(new Point(targetPoint.x, midY));
            } else {
                // Horizontal L-shape
                double midX = (sourcePoint.x + targetPoint.x) / 2;
                edge.routePoints.add(new Point(midX, sourcePoint.y));
                edge.routePoints.add(new Point(midX, targetPoint.y));
            }

            edge.routePoints.add(targetPoint);
        }

        /**
         * Create Z-shaped route
         */
        private void createZShapeRoute(BPMNEdge edge) {
            edge.routeType = RouteType.Z_SHAPE;
            edge.routePoints.clear();
            edge.isLongDistance = true;

            Point sourcePoint = edge.sourceNode.getConnectionPoint(edge.sourceConnectionSide);
            Point targetPoint = edge.targetNode.getConnectionPoint(edge.targetConnectionSide);

            edge.routePoints.add(sourcePoint);

            if (edge.isBackEdge) {
                // Back edge routing: bypass all nodes
                double rightX = findRightmostX() + CONNECTION_MARGIN;
                edge.routePoints.add(new Point(rightX, sourcePoint.y));
                edge.routePoints.add(new Point(rightX, targetPoint.y));
            } else {
                // Normal Z-shaped routing
                double midY1 = sourcePoint.y + VERTICAL_SPACING / 2;
                double midY2 = targetPoint.y - VERTICAL_SPACING / 2;

                edge.routePoints.add(new Point(sourcePoint.x, midY1));
                edge.routePoints.add(new Point(targetPoint.x, midY1));
                edge.routePoints.add(new Point(targetPoint.x, midY2));
            }

            edge.routePoints.add(targetPoint);
        }

        /**
         * Find rightmost X coordinate
         */
        private double findRightmostX() {
            return graph.nodes.values().stream()
                .mapToDouble(node -> {
                    Point pos = node.layoutPosition != null ? node.layoutPosition : new Point(node.originalX, node.originalY);
                    return pos.x + node.width;
                })
                .max()
                .orElse(0);
        }

        /**
         * Optimize edge crossings
         */
        private void optimizeEdgeCrossings() {
            System.out.println("Optimizing edge crossings...");

            List<BPMNEdge> edges = new ArrayList<>(graph.edges.values());

            // Calculate crossing count
            for (BPMNEdge edge : edges) {
                edge.crossingCount = 0;
                for (BPMNEdge other : edges) {
                    if (!edge.equals(other) && edge.crossesWith(other)) {
                        edge.crossingCount++;
                    }
                }
            }

            System.out.println("Crossing optimization completed");
        }

        /**
         * Post-processing: grid snapping and conflict resolution
         */
        private void postProcessLayout() {
            System.out.println("Starting post-processing...");

            // Grid snapping
            for (BPMNNode node : graph.nodes.values()) {
                if (node.layoutPosition != null) {
                    node.layoutPosition = node.layoutPosition.snapToGrid(GRID_SIZE);
                }
            }

            // Resolve node overlaps
            resolveNodeOverlaps();

            // Final edge path adjustment
            for (BPMNEdge edge : graph.edges.values()) {
                for (int i = 0; i < edge.routePoints.size(); i++) {
                    edge.routePoints.set(i, edge.routePoints.get(i).snapToGrid(GRID_SIZE));
                }
            }

            System.out.println("Post-processing completed");
        }

        /**
         * Resolve node overlap issues
         */
        private void resolveNodeOverlaps() {
            System.out.println("Resolving node overlaps...");

            boolean hasOverlap;
            int maxIterations = 10;
            int iteration = 0;

            do {
                hasOverlap = false;
                iteration++;

                for (int i = 0; i < graph.layers.size(); i++) {
                    List<BPMNNode> layer = graph.layers.get(i);

                    for (int j = 0; j < layer.size() - 1; j++) {
                        BPMNNode node1 = layer.get(j);
                        BPMNNode node2 = layer.get(j + 1);

                        if (node1.overlapsWith(node2)) {
                            // Adjust node position
                            double gap = MIN_NODE_DISTANCE;
                            double newX = node1.layoutPosition.x + node1.width + gap;
                            node2.layoutPosition = new Point(newX, node2.layoutPosition.y);
                            hasOverlap = true;
                        }
                    }
                }
            } while (hasOverlap && iteration < maxIterations);

            System.out.println("Node overlap resolution completed, iterations: " + iteration);
        }
    }

    // Main class member variables
    private ObjectMapper objectMapper = new ObjectMapper();
    private BPMNGraph graph = new BPMNGraph();

    /**
     * Optimize BPMN flowchart layout - main entry method
     */
    public void optimizeLayout(String inputJsonFile, String outputJsonFile) throws IOException {
        System.out.println("=== BPMN Professional Layout Engine Started ===");
        System.out.println("Input file: " + inputJsonFile);
        System.out.println("Output file: " + outputJsonFile);

        // Read input JSON
        JsonNode rootNode = objectMapper.readTree(new File(inputJsonFile));

        // Step 1: Parse JSON and build BPMN graph structure
        parseJsonAndBuildBPMNGraph(rootNode);

        // Step 2: Execute professional BPMN layout algorithm
        BPMNLayoutEngine layoutEngine = new BPMNLayoutEngine(graph);
        layoutEngine.executeLayout();

        // Step 3: Update JSON and save results
        updateJsonAndSave(rootNode, outputJsonFile);

        // Output statistics
        printLayoutStatistics();

        System.out.println("=== BPMN Layout Optimization Completed ===");
    }

    /**
     * Parse JSON and build BPMN graph structure
     */
    private void parseJsonAndBuildBPMNGraph(JsonNode rootNode) {
        System.out.println("Starting JSON data parsing...");

        // Get processModel node
        JsonNode processModel = null;
        if (rootNode.has("vueProcessData")) {
            processModel = rootNode.get("vueProcessData").get("processModel");
        } else if (rootNode.has("processModel")) {
            processModel = rootNode.get("processModel");
        }

        if (processModel == null) {
            throw new IllegalArgumentException("processModel node not found");
        }

        // First pass: create all nodes
        Iterator<Map.Entry<String, JsonNode>> fields = processModel.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (value.has("vertex") && "true".equals(value.get("vertex").asText())) {
                // Parse node
                BPMNNode node = parseBPMNNode(key, value);
                graph.addNode(node);
            }
        }

        // Second pass: create all edges
        fields = processModel.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (value.has("edge") && "true".equals(value.get("edge").asText())) {
                // Parse edge
                BPMNEdge edge = parseBPMNEdge(key, value);
                if (edge != null) {
                    graph.addEdge(edge);
                }
            }
        }

        System.out.println("JSON parsing completed - Nodes: " + graph.nodes.size() + ", Edges: " + graph.edges.size());
    }
