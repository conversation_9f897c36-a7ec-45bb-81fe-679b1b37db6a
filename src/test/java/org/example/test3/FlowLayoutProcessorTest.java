package org.example.test3;

import org.junit.Before;
import org.junit.Test;
import org.junit.After;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.junit.Assert.*;

/**
 * 流程图布局处理器测试类
 */
public class FlowLayoutProcessorTest {
    
    private FlowLayoutProcessor processor;
    private String testInputFile;
    private String testOutputFile;
    
    @Before
    public void setUp() {
        processor = new FlowLayoutProcessor();
        testInputFile = "src/main/java/org/example/test3/processDesignModel.json";
        testOutputFile = "src/main/java/org/example/test3/processDesignModel_test_output.json";
    }
    
    @After
    public void tearDown() {
        // 清理测试输出文件
        try {
            Files.deleteIfExists(Paths.get(testOutputFile));
        } catch (IOException e) {
            System.err.println("清理测试文件失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testInputFileExists() {
        File inputFile = new File(testInputFile);
        assertTrue("输入文件应该存在", inputFile.exists());
        assertTrue("输入文件应该可读", inputFile.canRead());
        assertTrue("输入文件不应该为空", inputFile.length() > 0);
    }
    
    @Test
    public void testProcessFlowLayout() {
        try {
            // 执行布局处理
            processor.processFlowLayout(testInputFile, testOutputFile);
            
            // 验证输出文件是否生成
            File outputFile = new File(testOutputFile);
            assertTrue("输出文件应该被创建", outputFile.exists());
            assertTrue("输出文件不应该为空", outputFile.length() > 0);
            
            System.out.println("测试成功：流程图布局处理完成");
            System.out.println("输出文件大小: " + outputFile.length() + " 字节");
            
        } catch (Exception e) {
            fail("流程图布局处理不应该抛出异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testFlowNodeCreation() {
        // 测试FlowNode对象创建
        FlowNode node = new FlowNode("test-id", "测试节点", 1);
        
        assertEquals("节点ID应该正确设置", "test-id", node.getId());
        assertEquals("节点名称应该正确设置", "测试节点", node.getNodeName());
        assertEquals("节点类型应该正确设置", Integer.valueOf(1), node.getNodeType());
        assertTrue("开始节点应该被正确识别", node.isStartNode());
        assertEquals("开始节点优先级应该为1", 1, node.getNodeTypePriority());
    }
    
    @Test
    public void testFlowConnectionCreation() {
        // 测试FlowConnection对象创建
        FlowConnection.NodeReference source = new FlowConnection.NodeReference("source-id");
        FlowConnection.NodeReference target = new FlowConnection.NodeReference("target-id");
        FlowConnection connection = new FlowConnection("conn-id", source, target);
        
        assertEquals("连线ID应该正确设置", "conn-id", connection.getId());
        assertEquals("源节点ID应该正确", "source-id", connection.getSourceId());
        assertEquals("目标节点ID应该正确", "target-id", connection.getTargetId());
        assertTrue("连线应该是有效的", connection.isValidConnection());
        assertFalse("不应该是重新提交连线", connection.isRecallConnection());
    }
    
    @Test
    public void testSpecialNodeIdentification() {
        // 测试特殊节点识别
        FlowNode recallNode = new FlowNode("recall", "重新提交", 4);
        FlowNode byStartNode = new FlowNode("byStart", "byStart节点", 9);
        FlowNode normalNode = new FlowNode("normal", "普通节点", 6);
        
        assertTrue("recall节点应该被识别为重新提交节点", recallNode.isRecallNode());
        assertTrue("byStart节点应该被识别为byStart节点", byStartNode.isByStartNode());
        assertFalse("普通节点不应该被识别为特殊节点", normalNode.isRecallNode());
        assertFalse("普通节点不应该被识别为byStart节点", normalNode.isByStartNode());
    }
    
    @Test
    public void testGeometryCalculation() {
        // 测试几何坐标计算
        FlowNode.Geometry geometry = new FlowNode.Geometry(100, 200, 180, 110);
        
        assertEquals("X坐标应该正确", 100.0, geometry.getX(), 0.01);
        assertEquals("Y坐标应该正确", 200.0, geometry.getY(), 0.01);
        assertEquals("宽度应该正确", 180.0, geometry.getWidth(), 0.01);
        assertEquals("高度应该正确", 110.0, geometry.getHeight(), 0.01);
        
        // 测试坐标更新
        geometry.setX(300);
        geometry.setY(400);
        assertEquals("更新后X坐标应该正确", 300.0, geometry.getX(), 0.01);
        assertEquals("更新后Y坐标应该正确", 400.0, geometry.getY(), 0.01);
    }
    
    @Test
    public void testNodeTypePriority() {
        // 测试节点类型优先级
        FlowNode startNode = new FlowNode("start", "开始", 1);
        FlowNode initNode = new FlowNode("init", "初始化", 2);
        FlowNode serviceNode = new FlowNode("service", "服务", 9);
        FlowNode gatewayNode = new FlowNode("gateway", "网关", 13);
        
        assertTrue("开始节点优先级应该高于初始化节点", 
                startNode.getNodeTypePriority() < initNode.getNodeTypePriority());
        assertTrue("初始化节点优先级应该高于服务节点", 
                initNode.getNodeTypePriority() < serviceNode.getNodeTypePriority());
        assertTrue("服务节点优先级应该高于网关节点", 
                serviceNode.getNodeTypePriority() < gatewayNode.getNodeTypePriority());
    }
    
    /**
     * 集成测试：完整的流程图处理流程
     */
    @Test
    public void testCompleteFlowProcessing() {
        try {
            System.out.println("开始集成测试...");
            
            // 执行完整的流程图处理
            processor.processFlowLayout(testInputFile, testOutputFile);
            
            // 验证结果
            File outputFile = new File(testOutputFile);
            assertTrue("输出文件必须存在", outputFile.exists());
            
            // 验证输出文件大小合理（不能太小，说明有内容）
            long outputSize = outputFile.length();
            assertTrue("输出文件大小应该大于1KB", outputSize > 1024);
            
            System.out.println("集成测试成功完成");
            System.out.println("输入文件: " + testInputFile);
            System.out.println("输出文件: " + testOutputFile);
            System.out.println("输出文件大小: " + outputSize + " 字节");
            
        } catch (Exception e) {
            fail("集成测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
