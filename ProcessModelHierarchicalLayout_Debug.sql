DELIMITER $$

DROP PROCEDURE IF EXISTS ProcessModelHierarchicalLayout$$

CREATE PROCEDURE ProcessModelHierarchicalLayout(
    IN p_process_id VARCHAR(100)
)
BEGIN
    -- ==================== 变量声明区域 ====================
    DECLARE v_error_count INT DEFAULT 0;           -- 错误计数器
    DECLARE v_start_node_id VARCHAR(100) DEFAULT NULL;  -- 开始节点ID
    DECLARE v_updated_json JSON;                   -- 更新后的JSON数据
    DECLARE v_node_count INT DEFAULT 0;            -- 节点总数
    DECLARE v_edge_count INT DEFAULT 0;            -- 连线总数

    -- 异常处理：如果发生SQL异常，回滚事务并重新抛出异常
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    -- ==================== 开始事务处理 ====================
    START TRANSACTION;

    -- 输出处理开始信息
    SELECT CONCAT('=== 开始处理流程ID: ', p_process_id, ' ===') AS 处理状态;

    -- ==================== 1. 输入参数验证 ====================
    -- 检查流程ID是否为空
    IF p_process_id IS NULL OR p_process_id = '' THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '流程ID不能为空';
    END IF;

    -- 检查指定的流程是否存在于数据库中
    SELECT COUNT(*) INTO v_error_count
    FROM com_paasit_pai_core_processDesignerObj
    WHERE PId = p_process_id;

    IF v_error_count = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '指定的流程ID不存在';
    END IF;

    -- ==================== 2. 创建临时表存储节点信息 ====================
    -- 删除可能存在的临时表
    DROP TEMPORARY TABLE IF EXISTS tmp_nodes;
    -- 创建节点临时表，用于存储节点信息和计算结果
    CREATE TEMPORARY TABLE tmp_nodes (
        id VARCHAR(100) PRIMARY KEY,           -- 节点ID
        nodeType INT,                          -- 节点类型（1=开始，2=任务，3=结束，4=recall等）
        nodeName VARCHAR(500),                 -- 节点名称
        source_id VARCHAR(100),                -- 源节点ID（暂未使用）
        target_id VARCHAR(100),                -- 目标节点ID（暂未使用）
        level_num INT DEFAULT 0,               -- 节点所在层级（从1开始）
        level_order INT DEFAULT 0,             -- 节点在同层级中的排序
        x_coord INT DEFAULT 0,                 -- 计算后的X坐标
        y_coord INT DEFAULT 0,                 -- 计算后的Y坐标
        original_x INT DEFAULT 0,              -- 原始X坐标
        original_y INT DEFAULT 0               -- 原始Y坐标
    ) ENGINE=InnoDB;

    -- ==================== 3. 创建临时表存储连线信息 ====================
    -- 删除可能存在的临时表
    DROP TEMPORARY TABLE IF EXISTS tmp_edges;
    -- 创建连线临时表，用于存储连线关系和坐标信息
    CREATE TEMPORARY TABLE tmp_edges (
        id VARCHAR(100) PRIMARY KEY,           -- 连线ID
        source_id VARCHAR(100),                -- 源节点ID
        target_id VARCHAR(100),                -- 目标节点ID
        is_valid BOOLEAN DEFAULT FALSE,        -- 连线是否有效
        -- 连线坐标信息
        start_x INT DEFAULT 0,                 -- 起点X坐标（源节点连接点）
        start_y INT DEFAULT 0,                 -- 起点Y坐标（源节点连接点）
        end_x INT DEFAULT 0,                   -- 终点X坐标（目标节点连接点）
        end_y INT DEFAULT 0,                   -- 终点Y坐标（目标节点连接点）
        -- 路径信息
        path_points JSON DEFAULT NULL,         -- 中间路径点（JSON数组格式）
        line_type VARCHAR(20) DEFAULT 'straight', -- 连线类型：straight(直线), curved(曲线), polyline(折线)
        -- 计算状态
        is_calculated BOOLEAN DEFAULT FALSE    -- 坐标是否已计算
    ) ENGINE=InnoDB;

    -- ==================== 4. 解析JSON数据，提取节点信息 ====================
    -- 从JSON数据中解析出所有节点信息并插入临时表
    INSERT INTO tmp_nodes (id, nodeType, nodeName, original_x, original_y)
    SELECT
        JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.id')) AS id,                    -- 提取节点ID
        CAST(JSON_EXTRACT(node_data, '$.nodeType') AS UNSIGNED) AS nodeType,    -- 提取节点类型
        COALESCE(JSON_UNQUOTE(JSON_EXTRACT(node_data, '$.nodeName')), '') AS nodeName,  -- 提取节点名称
        COALESCE(CAST(JSON_EXTRACT(node_data, '$.geometry.x') AS SIGNED), 0) AS original_x,  -- 提取原始X坐标
        COALESCE(CAST(JSON_EXTRACT(node_data, '$.geometry.y') AS SIGNED), 0) AS original_y   -- 提取原始Y坐标
    FROM com_paasit_pai_core_processDesignerObj,
         JSON_TABLE(
                 processDesignerData,                                               -- 从JSON数据中
                 '$.*' COLUMNS (node_data JSON PATH '$')                           -- 提取每个对象
         ) AS jt
    WHERE PId = p_process_id                                                   -- 指定流程ID
      AND JSON_EXTRACT(node_data, '$.vertex') = 'true'                        -- 只要顶点（节点）
      AND JSON_EXTRACT(node_data, '$.nodeType') IS NOT NULL;                  -- 节点类型不为空

    -- 统计解析到的节点数量
    SELECT COUNT(*) INTO v_node_count FROM tmp_nodes;
    SELECT CONCAT('解析到 ', v_node_count, ' 个节点') AS 解析状态;

    -- ==================== 5. 解析连线信息（仅用于层级计算，不处理连线坐标） ====================
    -- 从JSON数据中解析出所有连线关系并插入临时表
    INSERT INTO tmp_edges (id, source_id, target_id, is_valid)
    SELECT
        JSON_UNQUOTE(JSON_EXTRACT(edge_data, '$.id')) AS id,                   -- 提取连线ID
        JSON_UNQUOTE(JSON_EXTRACT(edge_data, '$.source.id')) AS source_id,    -- 提取源节点ID
        JSON_UNQUOTE(JSON_EXTRACT(edge_data, '$.target.id')) AS target_id,    -- 提取目标节点ID
        CASE
            WHEN JSON_EXTRACT(edge_data, '$.source.id') IS NOT NULL
                AND JSON_EXTRACT(edge_data, '$.target.id') IS NOT NULL
                THEN TRUE
            ELSE FALSE
            END AS is_valid                                                        -- 判断连线是否有效
    FROM com_paasit_pai_core_processDesignerObj,
         JSON_TABLE(
                 processDesignerData,                                               -- 从JSON数据中
                 '$.*' COLUMNS (edge_data JSON PATH '$')                           -- 提取每个对象
         ) AS jt
    WHERE PId = p_process_id                                                   -- 指定流程ID
      AND JSON_EXTRACT(edge_data, '$.edge') = 'true'                          -- 只要边（连线）
      AND JSON_EXTRACT(edge_data, '$.source.id') IS NOT NULL                  -- 源节点不为空
      AND JSON_EXTRACT(edge_data, '$.target.id') IS NOT NULL;                 -- 目标节点不为空

    -- 统计解析到的有效连线数量
    SELECT COUNT(*) INTO v_edge_count FROM tmp_edges WHERE is_valid = TRUE;
    SELECT CONCAT('解析到 ', v_edge_count, ' 条有效连线') AS 解析状态;

    -- ==================== 6. 找到开始节点 ====================
    -- 查找开始节点（nodeType = 1），作为层级计算的起点
    SELECT id INTO v_start_node_id
    FROM tmp_nodes
    WHERE nodeType = 1                                                         -- 开始节点类型为1
    LIMIT 1;

    -- 如果没有找到开始节点，抛出异常
    IF v_start_node_id IS NULL THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '未找到开始节点 (nodeType = 1)';
    END IF;

    SELECT CONCAT('找到开始节点: ', v_start_node_id) AS 开始节点;

    -- 测试点：到这里应该没有问题
    SELECT '=== 基础数据解析完成，开始层级计算 ===' AS 测试状态;

    -- 提交事务，使所有更改生效
    COMMIT;

    -- 清理临时表，释放内存
    DROP TEMPORARY TABLE IF EXISTS tmp_nodes;
    DROP TEMPORARY TABLE IF EXISTS tmp_edges;
    
    -- 输出最终完成状态
    SELECT '=== 调试版本执行完成 ===' AS 最终状态;

END$$

DELIMITER ;
