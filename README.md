# BPM流程图自动布局工具

## 项目概述

这是一个基于Java的BPM流程图自动布局工具，能够智能分析流程图结构并应用最适合的布局算法，特别支持包含循环引用的复杂流程图。

## 核心特性

### 🎯 智能布局算法选择
- **自动检测图结构**：分析节点和边的关系，检测循环引用
- **智能算法选择**：根据图的复杂度自动选择最适合的布局算法
- **多种布局支持**：层次布局、有机布局、圆形布局、混合布局

### 🔄 循环引用支持
- **循环检测**：使用DFS算法检测图中的循环
- **混合布局**：对局部使用层次布局，对整体应用有机布局优化
- **复杂图处理**：支持多循环、大规模流程图的处理

### 📊 布局算法详解

| 算法类型 | 适用场景 | 特点 |
|---------|---------|------|
| **AUTO** | 自动选择 | 根据图结构智能选择最佳算法 |
| **HIERARCHICAL** | 无循环DAG | 清晰的层次结构，从上到下 |
| **ORGANIC** | 有循环复杂图 | 自然的有机布局，适合复杂关系 |
| **CIRCLE** | 简单循环图 | 圆形布局，适合展示循环关系 |
| **HYBRID** | 复杂多循环图 | 局部层次 + 整体有机优化 |

## 快速开始

### 环境要求
- Java 8 或更高版本
- Maven 3.6 或更高版本

### 编译项目
```bash
mvn clean compile
```

### 运行示例

#### 1. 自动选择算法（推荐）
```bash
mvn exec:java -Dexec.mainClass="org.example.test1.ProcessModelLayouter"
```

#### 2. 指定布局算法
```bash
# 使用混合布局
mvn exec:java -Dexec.mainClass="org.example.test1.ProcessModelLayouter" -Dexec.args="HYBRID"

# 使用层次布局
mvn exec:java -Dexec.mainClass="org.example.test1.ProcessModelLayouter" -Dexec.args="HIERARCHICAL"

# 使用有机布局
mvn exec:java -Dexec.mainClass="org.example.test1.ProcessModelLayouter" -Dexec.args="ORGANIC"

# 使用圆形布局
mvn exec:java -Dexec.mainClass="org.example.test1.ProcessModelLayouter" -Dexec.args="CIRCLE"
```

#### 3. 指定输入文件
```bash
# 处理包含vueProcessData结构的JSON文件
mvn exec:java -Dexec.mainClass="org.example.test1.ProcessModelLayouter" -Dexec.args="\"atestfile/合同审核__总部职能-财务部_合同审批流程【BPM未美化的json】.json\""

# 指定算法和文件
mvn exec:java -Dexec.mainClass="org.example.test1.ProcessModelLayouter" -Dexec.args="HYBRID \"atestfile/your-file.json\""
```

## 运行示例输出

```
开始处理BPM流程图布局...
输入文件: atestfile/processModel.json
输出文件: processModel_layouted.json

正在解析流程图结构...
解析完成: 51 个节点, 98 条边

正在分析图结构并选择布局算法...
图结构分析: GraphStats{nodes=51, edges=98, hasCycle=true, components=3}
检测到循环引用，分析循环复杂度...
发现 6 个循环
循环分析: 平均长度=3.0, 最大长度=13, 总数=6
检测到复杂多循环结构，使用混合布局（局部层次+整体有机优化）

正在应用布局算法: HYBRID
应用混合布局算法...
第一阶段：局部层次布局
发现 3 个强连通分量
处理分量 1，包含 49 个节点
第二阶段：整体有机布局优化
开始全局有机优化，迭代次数: 50
优化进度: 0% -> 20% -> 40% -> 60% -> 80% -> 100%
全局优化完成

✅ 布局处理完成!
使用算法: HYBRID
处理时间: 784ms
输出文件: processModel_layouted.json
推荐理由: 复杂循环图结构，混合布局先保持局部层次结构，再进行整体优化
```

## 技术架构

### 核心组件
- **ProcessModelLayouter**: 主处理类
- **LayoutEngine**: 布局算法引擎
- **CycleDetector**: 循环检测器
- **LayoutSelector**: 智能布局选择器
- **NodeInfo/EdgeInfo**: 数据结构类

### 混合布局算法流程
1. **图结构分析**: 解析JSON，构建图的邻接表
2. **循环检测**: 使用DFS检测强连通分量和循环
3. **分量分离**: 将图分解为多个强连通分量
4. **局部层次布局**: 对每个分量应用层次布局
5. **全局有机优化**: 使用力导向算法进行整体优化
6. **坐标更新**: 将新坐标写回JSON文件

### 算法选择策略
```
if (无循环) {
    return HIERARCHICAL;
} else if (简单单循环 && 节点数 <= 20) {
    return CIRCLE;
} else if (复杂多循环 && 节点数 > 30) {
    return HYBRID;
} else {
    return ORGANIC;
}
```

## 配置参数

### 布局参数
- `NODE_SPACING_X`: 节点水平间距 (默认: 150px)
- `NODE_SPACING_Y`: 节点垂直间距 (默认: 100px)
- `FORCE_CONSTANT`: 有机布局斥力强度 (默认: 120)
- `MAX_ITERATIONS`: 有机布局最大迭代次数 (默认: 1000)

### 性能限制
- 推荐最大节点数: 1000
- 推荐最大边数: 2000
- 最大处理时间: 30秒

## 输入输出格式

### 输入文件
- 路径: `atestfile/processModel.json`
- 格式: BPM流程图JSON结构
- 要求: 包含节点的geometry信息和边的连接关系

### 输出文件
- 路径: `processModel_layouted.json`
- 格式: 与输入相同，但坐标已优化
- 变更: 仅更新geometry中的x, y坐标

## 项目结构

```
src/main/java/org/example/
├── ProcessModelLayouter.java    # 主类
├── LayoutEngine.java           # 布局算法引擎
├── CycleDetector.java          # 循环检测器
├── LayoutSelector.java         # 布局选择器
├── LayoutConfig.java           # 配置类
├── NodeInfo.java              # 节点信息类
└── EdgeInfo.java              # 边信息类
```

## 扩展性

### 添加新的布局算法
1. 在`LayoutConfig.LayoutAlgorithm`中添加新枚举值
2. 在`LayoutEngine`中实现新的布局方法
3. 在`LayoutSelector`中添加选择逻辑
4. 更新`ProcessModelLayouter`中的switch语句

### 自定义配置
可以通过修改`LayoutConfig`类中的常量来调整布局参数。

## 输入输出格式

### 输入文件支持多种JSON结构
1. **直接processModel结构**:
   ```json
   {
     "processModel": { ... }
   }
   ```

2. **vueProcessData结构**:
   ```json
   {
     "vueProcessData": {
       "processModel": { ... }
     }
   }
   ```

3. **嵌套vueProcessData结构**:
   ```json
   {
     "vueProcessData": {
       "processData": {
         "processModel": { ... }
       }
     }
   }
   ```

### 输出文件
- **路径**: `{输入文件名}_layouted.json`
- **格式**: **保持完整的原始JSON结构**
- **变更**: 仅更新processModel中geometry的x, y坐标
- **特点**: 所有其他字段和层级结构完全保留

## 注意事项

1. **循环处理**: 混合布局算法特别适合处理包含循环引用的复杂BPM流程图
2. **性能考虑**: 对于超大型图（>1000节点），建议使用层次布局以获得更好的性能
3. **坐标保持**: 算法会保持节点的原始尺寸，只更新位置坐标
4. **错误处理**: 系统会自动检测并警告不合适的算法选择

## 许可证

本项目基于MIT许可证开源。
